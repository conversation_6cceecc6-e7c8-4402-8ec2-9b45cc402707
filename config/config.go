package config

import (
	"sync/atomic"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/xconf/remote"
)

var conf atomic.Pointer[Config]

func Get() *Config { return conf.Load() }

type Config struct {
	Register      bool `xconf:"register"`
	WorkerNum     int  `xconf:"worker_num"`
	FrequencyTime int  `xconf:"frequency_time"`
	BIReporting   bool `xconf:"bi_reporting"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	// default config
	c := &Config{
		Register:    false,
		BIReporting: true,
	}

	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}

	conf.Store(c)
	return nil
}
