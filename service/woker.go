package service

import (
	"context"
	"score/config"

	pb "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/errgroup"
	"google.golang.org/protobuf/proto"
)

func WorkerRun() {
	group := new(errgroup.Group)
	for i := 0; i < config.Get().WorkerNum; i++ {
		group.Go(func() error {
			for m := range runnerBuf {
				ictx := kafkaCtx(context.Background(), m)
				logger := xlog.FromContext(ictx)
				msg := new(pb.Record)
				err := proto.Unmarshal(m.Value, msg)
				if err != nil {
					logger.Error("unmarshal message failed", xlog.Err(err))
					MetricsErrorOccurred.Add(1, "ProtoUnmarshal", err.Error())
					continue
				}
				logger.Info("receive message", xlog.Any("msg", msg))
				err = HandleMessage(ictx, msg)
				if err != nil {
					logger.Error("handle message failed", xlog.Err(err))
					MetricsErrorOccurred.Add(1, "HandelMessage", err.Error())
					MetricsConsumedMessages.Add(1, msg.ModelCode, "failed")
					//handle 出错也数据也需要入Clickhouse
					// continue
				} else {
					MetricsConsumedMessages.Add(1, msg.ModelCode, "successs")
				}
				chBacthBuf <- msg
			}
			xlog.Debug("worker exit")
			return nil
		})
	}
	group.Wait()
	close(chBacthBuf)
}
