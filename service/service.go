package service

import (
	"context"
	"errors"
	"score/broker"
	"score/model"
	"score/service/risktag"
	"score/shared"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"github.com/segmentio/kafka-go"
	pb "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/watch"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/pattern/watch/provider"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
)

type Service struct{}

var (
	service    *Service
	watcher    *watch.Watcher
	models     atomic.Pointer[map[uint32]map[string]*RiskModel]
	chBacthBuf = make(chan *pb.Record, 300)
	runnerBuf  = make(chan *kafka.Message, 100)
)

const (
	EtcdRiskTag = "/sparrow/risk/conf/tag"
)

func Get() *Service {
	return service
}

func Startup() error {
	var err error
	service, err = NewService()
	if err != nil {
		return err
	}
	xconf.RegisterReload(service.Reload)
	watcher, err = watch.StdConfig().Build()
	if err != nil {
		return err
	}
	watcher.Watch(EtcdRiskTag, func(res *watch.Response) error {
		return service.RiskTagReload()
	})
	return nil
}

func NewService() (*Service, error) {
	s := new(Service)
	err := s.Reload()
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (s *Service) RiskTagReload() error {
	ctx := context.Background()
	tags, err := model.GetRiskTags(ctx)
	if err != nil {
		return err
	}
	temp := make(map[uint32]map[string]*RiskModel)
	for _, v := range tags {
		if temp[uint32(v.ClientId)] == nil {
			temp[uint32(v.ClientId)] = make(map[string]*RiskModel)
		}
		if temp[uint32(v.ClientId)][v.ModelId] == nil {
			temp[uint32(v.ClientId)][v.ModelId] = new(RiskModel)
		}
		switch v.Code {
		case "account_interval":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Interval{IntervalTag: &risktag.AccountInterval{RiskTag: v}})
		case "device_interval":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Interval{IntervalTag: &risktag.DeviceInterval{RiskTag: v}})
		case "ip_interval":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Interval{IntervalTag: &risktag.IpInterval{RiskTag: v}})
		case "ip_frequency":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Frequency{BaseTag: &risktag.IpFrequency{RiskTag: v}})
		case "device_frequency":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Frequency{BaseTag: &risktag.DeviceFrequency{RiskTag: v}})
		case "account_frequency":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Frequency{BaseTag: &risktag.AccountFrequency{RiskTag: v}})
		case "ip_collect":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Collect{CollectTag: &risktag.IpCollect{RiskTag: v}})
		case "device_collect":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Collect{CollectTag: &risktag.DeviceCollect{RiskTag: v}})
		case "device_risk_tag":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.RiskTag{BaseTag: &risktag.DeviceRiskTag{RiskTag: v}})
		case "device_doid_exception":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.DeviceDoidException{BaseTag: &risktag.DoidExceptionNid{RiskTag: v}})
		case "device_doid_exception_for_role":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.DeviceDoidException{BaseTag: &risktag.DoidExceptionRole{RiskTag: v}})
		case "ip_active_interval":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Interval{IntervalTag: &risktag.IpRoleInterval{RiskTag: v}})
		case "ip_active_frequency":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Frequency{BaseTag: &risktag.IpRoleFrequency{RiskTag: v}})
		case "device_role_interval":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Interval{IntervalTag: &risktag.DeviceRoleInterval{RiskTag: v}})
		case "device_active_frequency":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Frequency{BaseTag: &risktag.DeviceRoleFrequency{RiskTag: v}})
		case "device_role_collect":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Collect{CollectTag: &risktag.DeviceRoleCollect{RiskTag: v}})
		case "device_risk_tag_for_role":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.RiskTag{BaseTag: &risktag.DeviceRiskTagForRole{RiskTag: v}})
		case "ip_role_collect":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Collect{CollectTag: &risktag.IpRoleCollect{RiskTag: v}})
		case "role_active_interval":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Interval{IntervalTag: &risktag.RoleInterval{RiskTag: v}})
		case "role_active_frequency":
			temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.Frequency{BaseTag: &risktag.RoleFrequency{RiskTag: v}})
			// case "device_regular":
			// 	temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.DeviceRegular{RiskTag: v})
			// case "ip_regular":
			// 	temp[uint32(v.ClientId)][v.ModelId].Add(&risktag.IpRegular{RiskTag: v})
		}
	}
	xlog.Info("risk_tag_reload", xlog.Any("tags", temp))
	models.Store(&temp)
	return nil
}

func (s *Service) Reload() error {
	return s.RiskTagReload()
}

func Run(ctx context.Context) {
	reader := broker.Get()
	for {
		m, err := reader.ReadMessage(ctx)
		if err != nil {
			xlog.Error("read message failed", xlog.Err(err))
			MetricsErrorOccurred.Add(1, "kafka", "read_message_failed")
			if errors.Is(ctx.Err(), context.Canceled) {
				close(runnerBuf)
				return
			} else {
				// kakfa 异常处理，降配的时候有段时间不可用
				time.Sleep(3 * time.Second)
				continue
			}
		}
		xlog.Debug("read message", xlog.Any("message", m))
		runnerBuf <- &m
	}
}

func kafkaCtx(ctx context.Context, msg *kafka.Message) context.Context {
	var (
		requestId = uuid.New().String()
	)
	for _, v := range msg.Headers {
		if string(v.Key) == xnet.RequestId && string(v.Value) != "" {
			requestId = string(v.Value)
			break
		}
	}
	ctx = xlog.NewContext(ctx, xlog.L().With(xlog.RequestID(requestId)))
	return ctx
}

func getRiskModel(clientId uint32, modeCode string) (*RiskModel, error) {
	temp := *models.Load()
	if temp[clientId] == nil || temp[clientId][modeCode] == nil {
		return nil, shared.ErrModelNotExists
	}
	m := temp[clientId][modeCode]
	return m, nil
}

func CHRecordData() {
	var buffer = make([]*pb.Record, 0, 1000)
	var ticker = time.NewTicker(time.Second)
	// ctx := context.Background()
	defer ticker.Stop()
	for {
		select {
		case m, ok := <-chBacthBuf:
			if ok {
				buffer = append(buffer, m)
				if len(buffer) == 1000 {
					// err := chBatchInsert(ctx, buffer)
					// if err != nil {
					// 	xlog.Error("batch insert failed", xlog.Err(err))
					// 	MetricsErrorOccurred.Add(1, "clickhouse_batchinsert", err.Error())
					// }
					buffer = buffer[:0]
				}
			} else {
				if len(buffer) == 0 {
					xlog.Info("CHRecordData exit")
					return
				}
				// err := chBatchInsert(ctx, buffer)
				// if err != nil {
				// 	xlog.Error("batch insert failed", xlog.Err(err))
				// 	MetricsErrorOccurred.Add(1, "clickhouse_batchinsert", err.Error())
				// }
				xlog.Info("CHRecordData exit")
				return
			}
		case <-ticker.C:
			if len(buffer) > 0 {
				// err := chBatchInsert(ctx, buffer)
				// if err != nil {
				// 	xlog.Error("batch insert failed", xlog.Err(err))
				// 	MetricsErrorOccurred.Add(1, "clickhouse_batchinsert", err.Error())
				// }
				buffer = buffer[:0]
			}
		}
	}
}

func chBatchInsert(ctx context.Context, data []*pb.Record) error {
	// if err := model.BatchInsert(ctx, data); err != nil {
	// 	return err
	// }
	xlog.Info("batch insert success", xlog.Int("count", len(data)))
	MerticClickHouseBatchInsert.Add(float64(len(data)))
	return nil
}

func (s *Service) Run(ctx context.Context) {
	wg := sync.WaitGroup{}
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		WorkerRun()
	})
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		Run(ctx)
	})
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		CHRecordData()
	})
	wg.Wait()
}
