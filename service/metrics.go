package service

import "gitlab.papegames.com/fringe/sparrow/pkg/metric"

var (
	MetricsErrorOccurred = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "risk",
		Subsystem:   "score",
		Name:        "error_occurred",
		Help:        "risk score error occurred total",
		Labels:      []string{"kind", "reason"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsConsumedMessages = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "risk",
		Subsystem:   "score",
		Name:        "consumed_messages",
		Help:        "risk score consumed messages total",
		Labels:      []string{"model_code", "state"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsCalculateScorecastMs = metric.NewHistogramVec(&metric.HistogramVecOpts{
		Namespace:   "risk",
		Subsystem:   "score",
		Name:        "calc_score_milliseconds",
		Help:        "calc score milliseconds",
		Labels:      []string{"model_code", "state"},
		ConstLabels: metric.TargetLabels,
		Buckets:     metric.BucketRT,
	})

	MetricsCalculateTagScorecastMs = metric.NewHistogramVec(&metric.HistogramVecOpts{
		Namespace:   "risk",
		Subsystem:   "score",
		Name:        "calc_tag_score_milliseconds",
		Help:        "calc tag score milliseconds",
		Labels:      []string{"model_code", "tag_id", "code"},
		ConstLabels: metric.TargetLabels,
		Buckets:     metric.BucketRT,
	})

	MerticClickHouseBatchInsert = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "risk",
		Subsystem:   "score",
		Name:        "clickhouse_batch_insert",
		Help:        "clickhouse batch insert",
		ConstLabels: metric.TargetLabels,
	})
)
