package service

import (
	"encoding/json"
	"score/model"

	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
)

type RiskScoreExtra struct {
	EventID          int64           `json:"eventid,omitempty"`
	ClientID         string          `json:"clientid,omitempty"`
	DtEventTime      string          `json:"dteventtime"`
	PartEvent        string          `json:"part_event"`
	EventTimeStamp   int64           `json:"eventtimestamp,omitempty"`
	LoginRequestID   string          `json:"loginrequestid,omitempty"`
	DOID             string          `json:"doid,omitempty"`
	AppID            string          `json:"appid,omitempty"`
	VOpenID          string          `json:"vopenid,omitempty"`
	VRoleID          string          `json:"vroleid,omitempty"`
	RiskScoreTrigger int             `json:"riskscoretrigger,omitempty"`
	RiskScore        int64           `json:"riskscore,omitempty"`
	LabelScore       string          `json:"labelscore,omitempty"`
	RiskLabel        string          `json:"risklabel,omitempty"`
	LabelTTL         string          `json:"labelttl,omitempty"`
	LabelValue       string          `json:"labelvalue,omitempty"`
	RiskscoreScene   string          `json:"riskscorescene,omitempty"`
	Extra            json.RawMessage `json:"extra,omitempty"`
	ClientIP         string          `json:"clientip,omitempty"`
}

// FromMapToScoreEvent bi上报时间从请求中的额外参数获取
func NewBiEvent(requestId string,
	clientId uint32,
	appId string,
	doid string,
	nid string,
	ip string,
	tag *model.RiskTag,
	value string,
	score int64,
	ttl int64,
	sence string,
	roleid string,
) *RiskScoreExtra {
	id := time.Now().UnixNano()
	now := time.Now()
	event := &RiskScoreExtra{
		EventID:        id,
		PartEvent:      "RiskScoreExtra",
		DtEventTime:    now.Format(time.DateTime),
		EventTimeStamp: now.Unix(),
		LoginRequestID: requestId,
		RiskscoreScene: sence, //"login",
	}
	riskTag := xcast.ToString(tag.Id)
	event.ClientID = xcast.ToString(clientId)
	event.DOID = doid
	event.AppID = appId
	event.VOpenID = nid
	event.ClientIP = ip
	event.RiskScore = 0
	event.RiskScoreTrigger = 1
	event.LabelScore = xcast.ToString(score)
	event.RiskLabel = riskTag
	event.LabelValue = value
	event.LabelTTL = xcast.ToString(ttl)
	event.Extra = json.RawMessage(tag.ToJson())
	event.VRoleID = roleid
	return event
}

func (s *RiskScoreExtra) ToJson() []byte {
	b, _ := json.Marshal(s)
	return b
}
