package service

import (
	"context"
	"score/broker"
	"score/config"
	"score/service/risktag"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type RiskModel struct {
	Events []risktag.Eventer
}

func (m *RiskModel) Add(eventer risktag.Eventer) {
	m.Events = append(m.Events, eventer)
}

func (m *RiskModel) CalcScore(ctx context.Context, param *risktag.BaseParam) error {
	var ret = make([]*risktag.ScoreItem, 0, len(m.Events))
	for _, e := range m.Events {
		st := time.Now()
		if err := e.CalcScore(ctx, param, &ret); err != nil {
			MetricsErrorOccurred.Add(1, "risk_model", "risk_calc_score_failed")
			xlog.Error("calc score failed", xlog.String("event", e.GetTag().Code),
				xlog.Uint64("tag_id", e.GetTag().Id), xlog.String("err", err.Error()))
			continue
		} else {
			xlog.Debug("calc score success", xlog.String("event", e.GetTag().Code),
				xlog.Uint64("tag_id", e.GetTag().Id))
		}
		MetricsCalculateTagScorecastMs.Observe(float64(time.Since(st).Milliseconds()),
			e.GetTag().ModelId, xcast.ToString(e.GetTag().Id), e.GetTag().Code)
	}

	//BI Reporting
	if len(ret) > 0 && config.Get().BIReporting {
		var messages = make([]xkafka.Message, 0, len(ret))
		scene := param.Scene
		if scene == "" { //兼容老数据
			scene = "login"
		}
		for _, item := range ret {
			b := NewBiEvent(param.RequestId,
				param.ClientId,
				param.AppId,
				param.Doid,
				param.Nid,
				param.Ip,
				item.Tag,
				item.Value,
				item.Score,
				int64(item.TTL/time.Second),
				scene,
				param.RoleId,
			)
			messages = append(messages, xkafka.Message{
				Value: b.ToJson(),
			})
		}
		xlog.Debug("calc score result", xlog.Any("result", ret), xlog.Any("messages", messages))
		err := broker.GetBI().WriteMessages(ctx, messages...)
		if err != nil {
			xlog.Error("write messages to kafka failed", xlog.String("err", err.Error()))
			MetricsErrorOccurred.Add(1, "risk_model", "bi_report_failed")
			return err
		}
	}
	return nil
}

func (m *RiskModel) Record(ctx context.Context, param *risktag.BaseParam) error {
	//interval 类型事件，直接存储redis，直接计分
	for _, e := range m.Events {
		if err := e.EventRecord(ctx, param); err != nil {
			MetricsErrorOccurred.Add(1, "risk_model", "event_record_failed")
			continue
		}
	}
	return nil
}
