package service

import (
	"context"
	"score/config"
	"score/database"
	"testing"

	pb "gitlab.papegames.com/biz/protobuf/risk/livetags"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
)

func TestMain(m *testing.M) {
	xconf.ReadInConfig()
	err := config.Startup()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}
	err = xdebug.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	m.Run()
}

func TestAccountFrequencyUseRedis(t *testing.T) {
	Convey("HandleMsg", t, func() {
		msg := &pb.Record{
			ClientId:  2008,
			ModelCode: "active_model",
			Nid:       "2317928",
			Doid:      "ciTdNfuHQLtueBO3cqKLMMXAX4TGY9FsjU4v",
			Ip:        "************",
			Action:    "login",
			AppId:     "5510044",
			Scene:     "active",
			RoleId:    "**********",
		}
		ctx := context.Background()
		NewService()
		err := HandleMessage(ctx, msg)
		So(err, ShouldEqual, nil)
	})
}
