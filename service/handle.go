package service

import (
	"context"
	"score/service/risktag"
	"time"

	pb "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func HandleMessage(ctx context.Context, msg *pb.Record) error {
	m, err := getRiskModel(msg.ClientId, msg.ModelCode)
	if err != nil {
		return err
	}
	t := time.Now()
	bp := &risktag.BaseParam{
		ClientId:  msg.ClientId,
		ModelCode: msg.ModelCode,
		Nid:       msg.Nid,
		Ip:        msg.Ip,
		Doid:      msg.Doid,
		Province:  msg.Province,
		RoleId:    msg.RoleId,
		Now:       t,
		Scene:     msg.Scene,
	}
	st := time.Now()
	err = m.CalcScore(ctx, bp)
	logger := xlog.FromContext(ctx)
	if err != nil {
		logger.Error("calc score error", xlog.String("error", err.Error()))
		MetricsCalculateScorecastMs.Observe(float64(time.Since(st).Milliseconds()), msg.ModelCode, "failure")
		return err
	}
	MetricsCalculateScorecastMs.Observe(float64(time.Since(st).Milliseconds()), msg.ModelCode, "success")
	//某些模型需要记录到redis中
	err = m.Record(ctx, bp)
	if err != nil {
		logger.Error("record  error", xlog.String("error", err.Error()))
		return err
	}
	return nil
}
