package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type IpCollect struct {
	*model.RiskTag
}

func (e *IpCollect) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *IpCollect) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_Ip, param.Ip)
}

func (e *IpCollect) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpCollect) GetMember(ctx context.Context, param *BaseParam) string {
	return param.Nid
}

func (e *IpCollect) ValidParam(ctx context.Context, param *BaseParam) bool {
	return param.Ip != ""
}
