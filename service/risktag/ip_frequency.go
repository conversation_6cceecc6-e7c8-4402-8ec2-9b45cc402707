package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type IpFrequency struct {
	*model.RiskTag
}

func (e *IpFrequency) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return false
}

func (e *IpFrequency) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpFrequency) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *IpFrequency) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_NId, param.Nid)
}
