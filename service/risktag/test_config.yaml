# Risk Tag 测试配置文件
# 用于单元测试和集成测试的配置

# 测试数据库配置
test_database:
  mysql:
    host: "localhost"
    port: 3306
    username: "test_user"
    password: "test_password"
    database: "test_risk_score"
    max_idle_conns: 5
    max_open_conns: 10
  
  redis:
    host: "localhost"
    port: 6379
    password: ""
    db: 1  # 使用测试数据库
    pool_size: 5
    max_retries: 2
  
  calc_redis:
    host: "localhost"
    port: 6379
    password: ""
    db: 2  # 使用测试数据库
    pool_size: 5
    max_retries: 2

# 测试标签配置
test_tags:
  frequency:
    - id: 1001
      client_id: 1001
      model_id: "test_model"
      code: "test_device_frequency"
      threshold: 3
      window: 60
      score: 50
      period: 300
    
    - id: 1002
      client_id: 1001
      model_id: "test_model"
      code: "test_ip_frequency"
      threshold: 5
      window: 120
      score: 40
      period: 600
  
  interval:
    - id: 2001
      client_id: 1001
      model_id: "test_model"
      code: "test_device_interval"
      threshold_ms: 5000
      score: 60
      period: 300
    
    - id: 2002
      client_id: 1001
      model_id: "test_model"
      code: "test_ip_interval"
      threshold_ms: 3000
      score: 70
      period: 600
  
  collect:
    - id: 3001
      client_id: 1001
      model_id: "test_model"
      code: "test_device_collect"
      threshold: 3
      window: 3600
      score: 80
      period: 300
    
    - id: 3002
      client_id: 1001
      model_id: "test_model"
      code: "test_ip_collect"
      threshold: 2
      window: 1800
      score: 90
      period: 600
  
  risk_tag:
    - id: 4001
      client_id: 1001
      model_id: "test_model"
      code: "test_device_risk"
      labels: ["high_risk", "fraud", "bot"]
      score: 100
      period: 1800

# 测试用户配置
test_users:
  - client_id: 1001
    model_code: "test_model"
    nid: "test_user_001"
    doid: "test_device_001"
    role_id: "test_role_001"
    ip: "*************"
    province: "Beijing"
  
  - client_id: 1001
    model_code: "test_model"
    nid: "test_user_002"
    doid: "test_device_002"
    role_id: "test_role_002"
    ip: "*************"
    province: "Shanghai"

# 测试场景配置
test_scenarios:
  high_frequency:
    description: "高频率操作测试"
    frequency_threshold: 10
    interval_ms: 1000
    duration_seconds: 60
  
  device_sharing:
    description: "设备共享测试"
    user_count: 5
    device_count: 1
    duration_seconds: 300
  
  risk_device:
    description: "风险设备测试"
    risk_labels: ["high_risk", "fraud"]
    device_count: 3
    user_count: 10

# 性能测试配置
performance_test:
  concurrent_users: 100
  requests_per_user: 1000
  test_duration_seconds: 300
  memory_limit_mb: 512
  cpu_limit_percent: 80

# 覆盖率配置
coverage:
  minimum_percentage: 80
  exclude_files:
    - "*_mock.go"
    - "*_generated.go"
  include_integration_tests: true

# 日志配置
logging:
  level: "debug"
  format: "json"
  output: "test_logs/risktag_test.log"
  max_size_mb: 100
  max_backups: 5
  max_age_days: 7
