package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestDeviceRiskTagNid(t *testing.T) {
	a := RiskTag{
		&DeviceRiskTag{
			RiskTag: &model.RiskTag{
				Id:        22,
				ClientId:  1025,
				AppId:     "RiskTag22",
				ModelId:   "test_model",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 60,
					},
					Threshold: model.Threshold{
						DeviceRiskLabels: []string{"b_pc_emulator,b_cloud_device"},
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("DeviceRiskTag", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			Doid:      "4wcYPlNJOW4yd1qxR6i6xU7YpH4UvdI8AE3O",
			Now:       time.Now(),
		}
		ctx := context.Background()

		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)

	})
}

func TestDeviceRiskTagRole(t *testing.T) {
	a := RiskTag{
		&DeviceRiskTag{
			RiskTag: &model.RiskTag{
				Id:        22,
				ClientId:  1025,
				AppId:     "RiskTag22",
				ModelId:   "test_model",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 60,
					},
					Threshold: model.Threshold{
						DeviceRiskLabels: []string{"b_pc_emulator,b_cloud_device"},
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("DeviceRiskTag", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			RoleId:    "126988",
			Doid:      "4wcYPlNJOW4yd1qxR6i6xU7YpH4UvdI8AE3O",
			Now:       time.Now(),
		}
		ctx := context.Background()

		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)

	})
}
