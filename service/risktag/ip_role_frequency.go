package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type IpRoleFrequency struct {
	*model.RiskTag
}

func (e *IpRoleFrequency) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpRoleFrequency) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *IpRoleFrequency) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return false
}

func (e *IpRoleFrequency) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_Ip, param.Ip)
}
