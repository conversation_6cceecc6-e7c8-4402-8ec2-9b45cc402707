#!/bin/bash

# Risk Tag Test Suite Runner
# 风险标签测试套件运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "检查测试依赖..."
    
    if ! command -v go &> /dev/null; then
        print_message $RED "错误: Go 未安装或不在 PATH 中"
        exit 1
    fi
    
    print_message $GREEN "✓ Go 版本: $(go version)"
}

# 清理测试环境
cleanup_test_env() {
    print_message $BLUE "清理测试环境..."
    
    # 清理可能的测试文件
    rm -f coverage.out coverage.html
    rm -f test_results.xml
    
    print_message $GREEN "✓ 测试环境已清理"
}

# 运行单元测试
run_unit_tests() {
    print_message $BLUE "运行单元测试..."
    
    # 运行所有测试并生成覆盖率报告
    go test -v -race -coverprofile=coverage.out -covermode=atomic ./... 2>&1 | tee test_output.log
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_message $GREEN "✓ 单元测试通过"
    else
        print_message $RED "✗ 单元测试失败"
        return 1
    fi
}

# 运行基准测试
run_benchmark_tests() {
    print_message $BLUE "运行基准测试..."
    
    go test -bench=. -benchmem -run=^$ ./... > benchmark_results.txt 2>&1
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 基准测试完成"
        print_message $YELLOW "基准测试结果:"
        cat benchmark_results.txt
    else
        print_message $RED "✗ 基准测试失败"
        return 1
    fi
}

# 生成覆盖率报告
generate_coverage_report() {
    print_message $BLUE "生成覆盖率报告..."
    
    if [ -f coverage.out ]; then
        # 生成HTML覆盖率报告
        go tool cover -html=coverage.out -o coverage.html
        
        # 显示覆盖率统计
        coverage_percent=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
        print_message $GREEN "✓ 代码覆盖率: $coverage_percent"
        print_message $YELLOW "HTML覆盖率报告已生成: coverage.html"
        
        # 检查覆盖率阈值
        coverage_num=$(echo $coverage_percent | sed 's/%//')
        if (( $(echo "$coverage_num >= 80" | bc -l) )); then
            print_message $GREEN "✓ 覆盖率达到要求 (≥80%)"
        else
            print_message $YELLOW "⚠ 覆盖率低于80%，建议增加测试用例"
        fi
    else
        print_message $RED "✗ 覆盖率文件不存在"
        return 1
    fi
}

# 运行特定的测试文件
run_specific_tests() {
    local test_pattern=$1
    print_message $BLUE "运行特定测试: $test_pattern"
    
    go test -v -run="$test_pattern" ./...
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 特定测试通过"
    else
        print_message $RED "✗ 特定测试失败"
        return 1
    fi
}

# 检查测试质量
check_test_quality() {
    print_message $BLUE "检查测试质量..."
    
    # 统计测试文件数量
    test_files=$(find . -name "*_test.go" | wc -l)
    source_files=$(find . -name "*.go" -not -name "*_test.go" | wc -l)
    
    print_message $YELLOW "源文件数量: $source_files"
    print_message $YELLOW "测试文件数量: $test_files"
    
    if [ $test_files -gt 0 ]; then
        test_ratio=$(echo "scale=2; $test_files / $source_files" | bc)
        print_message $YELLOW "测试文件比例: $test_ratio"
        
        if (( $(echo "$test_ratio >= 0.8" | bc -l) )); then
            print_message $GREEN "✓ 测试覆盖充分"
        else
            print_message $YELLOW "⚠ 建议增加更多测试文件"
        fi
    fi
    
    # 统计测试函数数量
    test_functions=$(grep -r "func Test" . --include="*_test.go" | wc -l)
    benchmark_functions=$(grep -r "func Benchmark" . --include="*_test.go" | wc -l)
    
    print_message $YELLOW "测试函数数量: $test_functions"
    print_message $YELLOW "基准测试函数数量: $benchmark_functions"
}

# 生成测试报告
generate_test_report() {
    print_message $BLUE "生成测试报告..."
    
    cat > test_report.md << EOF
# Risk Tag 测试报告

## 测试概览

- **测试时间**: $(date)
- **Go 版本**: $(go version)
- **测试文件数量**: $(find . -name "*_test.go" | wc -l)
- **测试函数数量**: $(grep -r "func Test" . --include="*_test.go" | wc -l)

## 覆盖率统计

$(go tool cover -func=coverage.out 2>/dev/null || echo "覆盖率数据不可用")

## 测试结果

\`\`\`
$(tail -20 test_output.log 2>/dev/null || echo "测试输出不可用")
\`\`\`

## 基准测试结果

\`\`\`
$(cat benchmark_results.txt 2>/dev/null || echo "基准测试结果不可用")
\`\`\`

## 建议

1. 保持代码覆盖率在80%以上
2. 定期运行基准测试检查性能
3. 为新功能添加相应的测试用例
4. 关注并发安全性测试

EOF

    print_message $GREEN "✓ 测试报告已生成: test_report.md"
}

# 主函数
main() {
    print_message $GREEN "=== Risk Tag 测试套件 ==="
    
    # 解析命令行参数
    case "${1:-all}" in
        "unit")
            check_dependencies
            cleanup_test_env
            run_unit_tests
            generate_coverage_report
            ;;
        "bench")
            check_dependencies
            run_benchmark_tests
            ;;
        "coverage")
            check_dependencies
            cleanup_test_env
            run_unit_tests
            generate_coverage_report
            ;;
        "quality")
            check_test_quality
            ;;
        "report")
            generate_test_report
            ;;
        "specific")
            if [ -z "$2" ]; then
                print_message $RED "错误: 请指定测试模式，例如: ./run_tests.sh specific TestFrequency"
                exit 1
            fi
            check_dependencies
            run_specific_tests "$2"
            ;;
        "all"|*)
            check_dependencies
            cleanup_test_env
            run_unit_tests
            generate_coverage_report
            run_benchmark_tests
            check_test_quality
            generate_test_report
            ;;
    esac
    
    print_message $GREEN "=== 测试完成 ==="
}

# 显示帮助信息
show_help() {
    cat << EOF
Risk Tag 测试套件运行脚本

用法: $0 [选项]

选项:
    all         运行所有测试 (默认)
    unit        只运行单元测试
    bench       只运行基准测试
    coverage    运行测试并生成覆盖率报告
    quality     检查测试质量
    report      生成测试报告
    specific    运行特定测试 (需要指定测试名称)
    help        显示此帮助信息

示例:
    $0                          # 运行所有测试
    $0 unit                     # 只运行单元测试
    $0 specific TestFrequency   # 运行特定测试
    $0 coverage                 # 生成覆盖率报告

EOF
}

# 检查参数
if [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 运行主函数
main "$@"
