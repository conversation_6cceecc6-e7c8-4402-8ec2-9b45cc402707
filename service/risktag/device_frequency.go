package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type DeviceFrequency struct {
	*model.RiskTag
}

func (e *DeviceFrequency) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return false
}

func (e *DeviceFrequency) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceFrequency) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *DeviceFrequency) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_DoId, param.Doid)
}
