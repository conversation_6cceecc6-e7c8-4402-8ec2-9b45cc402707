package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type DeviceRoleFrequency struct {
	*model.RiskTag
}

func (e *DeviceRoleFrequency) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return false
}

func (e *DeviceRoleFrequency) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceRoleFrequency) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *DeviceRoleFrequency) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_DoId, param.Doid)
}
