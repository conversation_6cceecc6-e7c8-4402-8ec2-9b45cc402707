package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DeviceRiskTag struct {
	*model.RiskTag
}

func (e *DeviceRiskTag) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetDeviceTagScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *DeviceRiskTag) RecordKey(ctx context.Context, param *BaseParam) string {
	return ""
}

func (e *DeviceRiskTag) GetTag() *model.RiskTag {
	return e.RiskTag
}
