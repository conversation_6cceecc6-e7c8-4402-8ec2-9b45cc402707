package risktag

import (
	"context"
	"score/model"
)

type Interval struct {
	IntervalTag
}

func (e *Interval) EventRecord(ctx context.Context, base *BaseParam) error {
	if !e.NeedRecord(ctx, base) {
		return nil
	}
	return model.SetLastTime(ctx, e.<PERSON>(ctx, base), base.Now, model.IntervalTTL)
}

func (e *Interval) CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error {
	key := e.<PERSON>ey(ctx, param)
	lastTime, err := model.GetLastTime(ctx, e.<PERSON>Key(ctx, param))
	if err != nil {
		return err
	}
	if lastTime == 0 {
		return nil
	}
	threshold, err := e.GetTag().GetThresholdScore()
	if err != nil {
		return err
	}
	since := param.Now.UnixMilli() - lastTime
	if since <= threshold.RequestIntervalPeriod {
		s, ttl, err := e.GetTag().GetBoolScoreAndTLL()
		if err != nil {
			return err
		}
		if s > 0 && ttl > 0 {
			*sItems = append(*sItems, &ScoreItem{
				Value: "1",
				Score: s,
				TTL:   ttl,
				Tag:   e.GetTag(),
			})
			return model.SetScore(ctx, key, s, ttl, "1")
		}
	}
	return nil
}
