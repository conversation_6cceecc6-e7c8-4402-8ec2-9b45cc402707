package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type AccountFrequency struct {
	*model.RiskTag
}

func (e *AccountFrequency) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return false
}

func (e *AccountFrequency) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *AccountFrequency) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *AccountFrequency) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_NId, param.Nid)
}
