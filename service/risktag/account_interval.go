package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type AccountInterval struct {
	*model.RiskTag
}

func (e *AccountInterval) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return true
}

func (e *AccountInterval) RecordKey(ctx context.Context, base *BaseParam) string {
	return model.GetIntervalKey(ctx, base.ClientId, base.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, base.Nid)
}

func (e *AccountInterval) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *AccountInterval) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}
