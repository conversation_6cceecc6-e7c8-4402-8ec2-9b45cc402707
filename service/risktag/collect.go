package risktag

import (
	"context"
	"score/model"
	"time"
)

type Collect struct {
	CollectTag
}

func (e *Collect) EventRecord(ctx context.Context, base *BaseParam) error {
	return nil
}

func (e *Collect) CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error {
	if !e.Valid<PERSON>m(ctx, param) {
		return nil
	}
	threshold, err := e.GetTag().GetThresholdScore()
	if err != nil {
		return err
	}
	var num = threshold.DeviceLoginOpenIDNumbers
	if num == 0 {
		return nil
	}
	window, err := e.GetTag().GetWindow()
	if err != nil {
		return err
	}
	var ret bool
	//超过一天的窗口，超过500个设备，使用Clickhouse
	// if window <= time.Hour*24 && num <= 500 {
	ret, err = e.UseRedis(ctx, param, window, num)
	// } else {
	// 	ret, err = e.<PERSON>(ctx, param, window, num)
	// }
	if err != nil {
		return err
	}
	key := e.<PERSON>(ctx, param)
	if ret {
		s, ttl, err := e.GetTag().GetBoolScoreAndTLL()
		if err != nil {
			return err
		}
		if s > 0 && ttl > 0 {
			*sItems = append(*sItems, &ScoreItem{
				Value: "1",
				Score: s,
				TTL:   ttl,
				Tag:   e.GetTag(),
			})
			return model.SetScore(ctx, key, s, ttl, "1")
		}
	}
	return nil
}

func (e *Collect) UseRedis(ctx context.Context, param *BaseParam,
	window time.Duration, num int64) (bool, error) {
	key := e.RecordKey(ctx, param)
	return model.CollectLimiter(ctx, key, e.GetMember(ctx, param), window, uint64(num))
}
