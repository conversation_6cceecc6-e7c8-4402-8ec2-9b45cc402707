package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type RoleFrequency struct {
	*model.RiskTag
}

func (e *RoleFrequency) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *RoleFrequency) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *RoleFrequency) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return false
}

func (e *RoleFrequency) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_RoleId, param.RoleId)
}
