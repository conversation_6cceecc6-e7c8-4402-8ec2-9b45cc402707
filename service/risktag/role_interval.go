package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type RoleInterval struct {
	*model.RiskTag
}

func (e *RoleInterval) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *RoleInterval) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *RoleInterval) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return param.RoleId != ""
}

func (e *RoleInterval) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_RoleId, param.RoleId)
}
