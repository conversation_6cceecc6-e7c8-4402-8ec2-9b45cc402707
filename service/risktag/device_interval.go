package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type DeviceInterval struct {
	*model.RiskTag
}

func (e *DeviceInterval) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return param.Doid != ""
}

func (e *DeviceInterval) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_DoId, param.Doid)
}

func (e *DeviceInterval) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceInterval) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}
