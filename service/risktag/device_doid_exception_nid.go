package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DoidExceptionNid struct {
	*model.RiskTag
}

func (e *DoidExceptionNid) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetDeviceTagScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *DoidExceptionNid) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DoidExceptionNid) RecordKey(ctx context.Context, param *BaseParam) string {
	return ""
}
