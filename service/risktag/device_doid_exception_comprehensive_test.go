package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestDeviceDoidException(t *testing.T) {
	Convey("TestDeviceDoidException", t, func() {
		deviceException := &DeviceDoidException{
			&DeviceRiskTag{
				RiskTag: &model.RiskTag{
					Id:        600,
					ClientId:  1006,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestDeviceDoidException",
					Code:      "test_device_doid_exception",
					Status:    1,
					TagUnit:   1,
					RiskType:  1,
					ScoreType: 1,
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							DeviceRiskLabels: []string{
								"exception_label1,exception_label2",
								"single_exception",
							},
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      1200,
							ResultScore: 95,
						},
					},
				},
			},
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		<PERSON>vey("当设备ID为空时", func() {
			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "", // 空的设备ID
				Now:       time.Now(),
			}

			// 清理测试数据
			scoreKey := deviceException.ScoreKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)

			err := deviceException.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 验证设备ID被设置为 ExcludeDevice
			So(param.Doid, ShouldEqual, ExcludeDevice)
		})

		Convey("当设备ID不为空时", func() {
			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "test_device_006",
				Now:       time.Now(),
			}

			originalDoid := param.Doid

			// 清理测试数据
			scoreKey := deviceException.ScoreKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)

			err := deviceException.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 验证设备ID保持不变
			So(param.Doid, ShouldEqual, originalDoid)
		})

		Convey("当没有配置风险标签时", func() {
			deviceExceptionNoLabels := &DeviceDoidException{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       601,
						ClientId: 1006,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{}, // 空的风险标签
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      1200,
								ResultScore: 95,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "test_device_006",
				Now:       time.Now(),
			}

			err := deviceExceptionNoLabels.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该直接返回，不进行计算
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("测试EventRecord", func() {
			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "test_device_006",
				Now:       time.Now(),
			}

			// EventRecord 应该什么都不做
			err := deviceException.EventRecord(ctx, param)
			So(err, ShouldBeNil)
		})
	})
}

func TestDeviceDoidExceptionNid(t *testing.T) {
	Convey("TestDeviceDoidExceptionNid", t, func() {
		deviceExceptionNid := &DeviceDoidException{
			&DoidExceptionNid{
				RiskTag: &model.RiskTag{
					Id:       602,
					ClientId: 1006,
					ModelId:  "test_model",
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							DeviceRiskLabels: []string{
								"nid_exception_label",
							},
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      900,
							ResultScore: 85,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1006,
			ModelCode: "test_model",
			Nid:       "test_user_006",
			Doid:      "test_device_006",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := deviceExceptionNid.ScoreKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)

		Convey("测试设备异常Nid标签", func() {
			err := deviceExceptionNid.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 测试键值生成
			deviceExceptionNidTag := deviceExceptionNid.BaseTag.(*DoidExceptionNid)

			// 测试 ScoreKey
			scoreKey := deviceExceptionNidTag.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:dv:")
			So(scoreKey, ShouldContainSubstring, "NId")
			So(scoreKey, ShouldContainSubstring, param.Nid)

			// 测试 RecordKey
			recordKey := deviceExceptionNidTag.RecordKey(ctx, param)
			So(recordKey, ShouldEqual, "") // DeviceDoidExceptionNid 不需要记录键
		})
	})
}

func TestDeviceDoidExceptionRole(t *testing.T) {
	Convey("TestDeviceDoidExceptionRole", t, func() {
		deviceExceptionRole := &DeviceDoidException{
			&DoidExceptionRole{
				RiskTag: &model.RiskTag{
					Id:       603,
					ClientId: 1006,
					ModelId:  "test_model",
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							DeviceRiskLabels: []string{
								"role_exception_label",
							},
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,
							ResultScore: 75,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1006,
			ModelCode: "test_model",
			RoleId:    "test_role_006",
			Doid:      "test_device_006",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := deviceExceptionRole.ScoreKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)

		Convey("测试设备异常Role标签", func() {
			err := deviceExceptionRole.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 测试键值生成
			deviceExceptionRoleTag := deviceExceptionRole.BaseTag.(*DoidExceptionRole)

			// 测试 ScoreKey
			scoreKey := deviceExceptionRoleTag.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:dv:")
			So(scoreKey, ShouldContainSubstring, "RoleId")
			So(scoreKey, ShouldContainSubstring, param.RoleId)

			// 测试 RecordKey
			recordKey := deviceExceptionRoleTag.RecordKey(ctx, param)
			So(recordKey, ShouldEqual, "") // DeviceDoidExceptionRole 不需要记录键
		})
	})
}

func TestDeviceDoidExceptionConstants(t *testing.T) {
	Convey("TestDeviceDoidExceptionConstants", t, func() {
		Convey("测试常量定义", func() {
			So(ExcludeDevice, ShouldEqual, "NONE")
		})
	})
}

func TestDeviceDoidExceptionErrorCases(t *testing.T) {
	Convey("TestDeviceDoidExceptionErrorCases", t, func() {
		Convey("测试无效的评分配置", func() {
			deviceException := &DeviceDoidException{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       604,
						ClientId: 1006,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{"test_label"},
							},
						},
						ScoreRange: []model.ScoreRange{}, // 空的评分范围
					},
				},
			}

			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "test_device_006",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := deviceException.CalcScore(ctx, param, &scoreItems)
			// 根据实际实现，这里可能返回错误
			So(err, ShouldNotBeNil)
		})

		Convey("测试 nil TagCaliber", func() {
			deviceException := &DeviceDoidException{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:         605,
						ClientId:   1006,
						ModelId:    "test_model",
						TagCaliber: nil, // nil TagCaliber
						ScoreRange: []model.ScoreRange{
							{
								Period:      600,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "test_device_006",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := deviceException.CalcScore(ctx, param, &scoreItems)
			// 应该返回错误
			So(err, ShouldNotBeNil)
		})
	})
}

func TestDeviceDoidExceptionIntegration(t *testing.T) {
	Convey("TestDeviceDoidExceptionIntegration", t, func() {
		Convey("完整的设备异常检测流程", func() {
			deviceException := &DeviceDoidException{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       606,
						ClientId: 1006,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{
									"high_risk,fraud",
									"bot",
									"suspicious,malware",
								},
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      1800,
								ResultScore: 100,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "test_device_006",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := deviceException.ScoreKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)

			err := deviceException.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 测试标签解析逻辑
			threshold, err := deviceException.GetTag().GetThresholdScore()
			So(err, ShouldBeNil)
			So(len(threshold.DeviceRiskLabels), ShouldEqual, 3)

			// 测试标签分割逻辑
			var allLabels [][]string
			for _, label := range threshold.DeviceRiskLabels {
				p := strings.Split(label, ",")
				allLabels = append(allLabels, p)
			}
			So(len(allLabels), ShouldEqual, 3)
			So(len(allLabels[0]), ShouldEqual, 2) // "high_risk,fraud"
			So(len(allLabels[1]), ShouldEqual, 1) // "bot"
			So(len(allLabels[2]), ShouldEqual, 2) // "suspicious,malware"
		})

		Convey("测试空设备ID的处理", func() {
			deviceException := &DeviceDoidException{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       607,
						ClientId: 1006,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{"test_label"},
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      600,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1006,
				ModelCode: "test_model",
				Nid:       "test_user_006",
				Doid:      "", // 空的设备ID
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := deviceException.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 验证设备ID被正确设置
			So(param.Doid, ShouldEqual, ExcludeDevice)
		})
	})
}
