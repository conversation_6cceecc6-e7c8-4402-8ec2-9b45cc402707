package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type IpRoleInterval struct {
	*model.RiskTag
}

func (e *IpRoleInterval) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return param.Ip != ""
}

func (e *IpRoleInterval) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpRoleInterval) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *IpRoleInterval) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_Ip, param.Ip)
}
