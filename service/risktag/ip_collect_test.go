package risktag

import (
	"context"
	"fmt"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestClickhouseIpCollect(t *testing.T) {
	a := Collect{
		&IpCollect{
			RiskTag: &model.RiskTag{
				Id:        222,
				ClientId:  1033,
				AppId:     "RiskTag22",
				ModelId:   "risk_model_all",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 3600 * 25,
					},
					Threshold: model.Threshold{
						DeviceLoginOpenIDNumbers: 2,
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("IpCollect", t, func() {
		param := &BaseParam{
			ClientId:  1033,
			ModelCode: "risk_model_all",
			Nid:       "126988",
			Ip:        "************",
			Now:       time.Now(),
		}
		ctx := context.Background()
		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)
		r, err := database.GetCaclRdb().Get(ctx, a.ScoreKey(ctx, param)).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})

}

func TestRedisIpCollect(t *testing.T) {
	a := Collect{
		&IpCollect{
			RiskTag: &model.RiskTag{
				Id:        222,
				ClientId:  1033,
				AppId:     "RiskTag22",
				ModelId:   "risk_model_all",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 3600 * 24,
					},
					Threshold: model.Threshold{
						DeviceLoginOpenIDNumbers: 2,
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("TestRedisIpCollect", t, func() {
		param := &BaseParam{
			ClientId:  1033,
			ModelCode: "risk_model_all",
			Nid:       "12698811",
			Ip:        "************",
			Now:       time.Now(),
		}
		ctx := context.Background()
		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		for i := 0; i < 10; i++ {
			param.Nid = fmt.Sprintf("%d", i)
			err := a.CalcScore(ctx, param, &ret)
			So(err, ShouldEqual, nil)
		}
		param.Nid = "12698811"
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)
		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})
}

func TestRedisIpRoleCollect(t *testing.T) {
	a := Collect{
		&IpRoleCollect{
			RiskTag: &model.RiskTag{
				Id:        222,
				ClientId:  1033,
				AppId:     "RiskTag22",
				ModelId:   "risk_model_all",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 3600 * 24,
					},
					Threshold: model.Threshold{
						DeviceLoginOpenIDNumbers: 2,
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("TestRedisIpRoleCollect", t, func() {
		param := &BaseParam{
			ClientId:  1033,
			ModelCode: "risk_model_all",
			RoleId:    "12698811",
			Ip:        "************",
			Now:       time.Now(),
		}
		ctx := context.Background()
		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		for i := 0; i < 10; i++ {
			param.RoleId = fmt.Sprintf("%d", i)
			err := a.CalcScore(ctx, param, &ret)
			So(err, ShouldEqual, nil)
		}
		r, err := database.GetCaclRdb().Get(ctx, a.ScoreKey(ctx, param)).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})
}
