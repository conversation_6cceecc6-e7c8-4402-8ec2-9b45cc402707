package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type IpRegular struct {
	*model.RiskTag
}

func (e *IpRegular) EventRecord(ctx context.Context, base *BaseParam) error {
	return nil
}

func (e *IpRegular) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpRegular) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIpRegularScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *IpRegular) CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error {
	// key := e.ScoreKey(ctx, param)
	// threshold, err := e.RiskTag.GetThresholdScore()
	// if err != nil {
	// 	return err
	// }
	// var percent = threshold.LoginProvincePercentage
	// var num = threshold.ProvinceSuccessiveLogin
	// if percent == 0 || num == 0 {
	// 	return nil
	// }
	// window, err := e.RiskTag.GetWindow()
	// if err != nil {
	// 	return err
	// }
	// var ret1, ret2 bool
	// total, n, err := model.QueryAccountRegion(ctx, param.ClientId, param.ModelCode,
	// 	param.Nid, param.Province, param.Now.Add(-window), param.Now)
	// if err != nil {
	// 	return err
	// }
	// //加上本次
	// n++
	// if percent > 0 && percent >= float64(n+1/total) {
	// 	ret1 = true
	// }
	// if num > 0 && n >= num {
	// 	ret2 = true
	// }
	// if ret2 || ret1 {
	// 	s, ttl, err := e.GetBoolScoreAndTLL()
	// 	if err != nil {
	// 		return err
	// 	}
	// 	if s > 0 && ttl > 0 {
	// 		*sItems = append(*sItems, &ScoreItem{
	// 			Value: "1",
	// 			Score: s,
	// 			TTL:   ttl,
	// 			Tag:   e.GetTag(),
	// 		})
	// 		return model.SetScore(ctx, key, s, ttl, "1")
	// 	}
	// }
	return nil
}
