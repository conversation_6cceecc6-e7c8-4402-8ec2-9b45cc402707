# service
register: false
worker_num: 200
# 频率限制小于次数使用redis，否则使用Clickhouse
frequency_time: 200
sparrow:
  log:
    file: "./logs/score"
    encoding: "console"
    rotate: "daily"
    buffer: 4096
    level: "debug"

  govern:
    enable: true
    host: "internal:8091"
 
  database:
    mysql:
      dataSource: "root:BozsCMdcrj@(************:3306)/pay_risk?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 5
      maxOpenConns: 5
    redis:
      addr: *************:6379
      password: nikki
      pool_size: 5
      max_retries: 2
    calc_redis:
      addr: *************:6379
      password: nikki
      pool_size: 5
      max_retries: 2      
  watch:
    KeyPath: "sparrow.watch.config"
    Config:
      endpoints:
      - *************:2379  
  broker:
    kafka:
      reader:
        Brokers:
        - *************:9092
        Topic: t-risk-score
        GroupID: score_gp1