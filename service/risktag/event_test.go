package risktag

import (
	"score/config"
	"score/database"
	"testing"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
)

func TestMain(m *testing.M) {
	xconf.ReadInConfig()
	err := config.Startup()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}
	err = xdebug.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	m.Run()
}
