package risktag

import (
	"testing"
)

// TestArrayContainsStandalone 独立的数组包含测试
func TestArrayContainsStandalone(t *testing.T) {
	tests := []struct {
		name     string
		arr      []string
		str      []string
		expected bool
	}{
		{
			name:     "所有元素都存在",
			arr:      []string{"high_risk", "fraud", "bot", "automation"},
			str:      []string{"high_risk", "fraud"},
			expected: true,
		},
		{
			name:     "部分元素不存在",
			arr:      []string{"high_risk", "fraud"},
			str:      []string{"high_risk", "bot"},
			expected: false,
		},
		{
			name:     "所有元素都不存在",
			arr:      []string{"normal", "safe"},
			str:      []string{"high_risk", "fraud"},
			expected: false,
		},
		{
			name:     "检查空数组",
			arr:      []string{"high_risk", "fraud"},
			str:      []string{},
			expected: true,
		},
		{
			name:     "源数组为空",
			arr:      []string{},
			str:      []string{"high_risk"},
			expected: false,
		},
		{
			name:     "两个数组都为空",
			arr:      []string{},
			str:      []string{},
			expected: true,
		},
		{
			name:     "单个元素匹配",
			arr:      []string{"high_risk", "fraud", "bot"},
			str:      []string{"fraud"},
			expected: true,
		},
		{
			name:     "重复元素",
			arr:      []string{"high_risk", "fraud", "high_risk"},
			str:      []string{"high_risk"},
			expected: true,
		},
		{
			name:     "大小写敏感",
			arr:      []string{"High_Risk", "FRAUD"},
			str:      []string{"high_risk"},
			expected: false,
		},
		{
			name:     "包含空字符串",
			arr:      []string{"", "test", ""},
			str:      []string{""},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ArrayContains(tt.arr, tt.str)
			if result != tt.expected {
				t.Errorf("ArrayContains(%v, %v) = %v, expected %v",
					tt.arr, tt.str, result, tt.expected)
			}
		})
	}
}

// BenchmarkArrayContainsStandalone 性能基准测试
func BenchmarkArrayContainsStandalone(b *testing.B) {
	arr := []string{"high_risk", "fraud", "bot", "automation", "suspicious", "malware"}
	str := []string{"high_risk", "fraud", "bot"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ArrayContains(arr, str)
	}
}

// TestArrayContainsEdgeCasesStandalone 边界情况测试
func TestArrayContainsEdgeCasesStandalone(t *testing.T) {
	// 测试 nil 数组
	var nilArr []string
	var nilStr []string

	result := ArrayContains(nilArr, nilStr)
	if !result {
		t.Errorf("ArrayContains(nil, nil) = %v, expected true", result)
	}

	result = ArrayContains(nilArr, []string{"test"})
	if result {
		t.Errorf("ArrayContains(nil, [test]) = %v, expected false", result)
	}

	// 测试长字符串
	longStr := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		longStr[i] = "very_long_string_with_many_characters_" + string(rune('a'+(i%26)))
	}

	result = ArrayContains(longStr, []string{longStr[0], longStr[500]})
	if !result {
		t.Errorf("ArrayContains with long strings failed")
	}

	// 测试特殊字符
	specialChars := []string{"@#$%", "中文", "🚀", "\n\t"}
	result = ArrayContains(specialChars, []string{"中文", "🚀"})
	if !result {
		t.Errorf("ArrayContains with special characters failed")
	}
}

// TestArrayContainsConcurrencyStandalone 并发安全测试
func TestArrayContainsConcurrencyStandalone(t *testing.T) {
	arr := []string{"high_risk", "fraud", "bot", "automation"}
	str := []string{"high_risk", "fraud"}

	// 启动多个 goroutine 并发调用
	done := make(chan bool, 50)

	for i := 0; i < 50; i++ {
		go func(index int) {
			defer func() { done <- true }()

			// 每个 goroutine 执行多次调用
			for j := 0; j < 100; j++ {
				result := ArrayContains(arr, str)
				if !result {
					t.Errorf("Concurrent ArrayContains failed at goroutine %d, iteration %d", index, j)
					return
				}
			}
		}(i)
	}

	// 等待所有 goroutine 完成
	for i := 0; i < 50; i++ {
		<-done
	}
}
