package risktag

import (
	"context"
	"score/model"
	"time"
)

type BaseParam struct {
	ClientId  uint32
	AppId     string
	ModelCode string
	Scene     string
	RequestId string
	Nid       string
	Doid      string
	Ip        string
	Province  string
	RoleId    string
	Now       time.Time
}

type ScoreItem struct {
	Value string
	Score int64
	TTL   time.Duration
	Tag   *model.RiskTag
}

type Eventer interface {
	GetTag() *model.RiskTag
	ScoreKey(ctx context.Context, param *BaseParam) string
	RecordKey(ctx context.Context, param *BaseParam) string
	EventRecord(ctx context.Context, param *BaseParam) error
	CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error
}

type BaseTag interface {
	GetTag() *model.RiskTag
	ScoreKey(ctx context.Context, param *BaseParam) string
	RecordKey(ctx context.Context, param *BaseParam) string
}

type IntervalTag interface {
	GetTag() *model.RiskTag
	ScoreKey(ctx context.Context, param *BaseParam) string
	RecordKey(ctx context.Context, param *BaseParam) string
	NeedRecord(ctx context.Context, param *BaseParam) bool
}

type CollectTag interface {
	GetTag() *model.RiskTag
	ScoreKey(ctx context.Context, param *BaseParam) string
	RecordKey(ctx context.Context, param *BaseParam) string
	GetMember(ctx context.Context, param *BaseParam) string
	ValidParam(ctx context.Context, param *BaseParam) bool
}
