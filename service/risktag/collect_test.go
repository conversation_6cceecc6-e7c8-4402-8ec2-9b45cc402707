package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestCollectCalcScore(t *testing.T) {
	Convey("TestCollectCalcScore", t, func() {
		// 创建测试用的 Collect 实例
		collect := &Collect{
			&DeviceCollect{
				RiskTag: &model.RiskTag{
					Id:        100,
					ClientId:  1001,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestCollect",
					Code:      "test_collect",
					Status:    1,
					TagUnit:   1, // bool
					RiskType:  1, // 增分
					ScoreType: 1, // 静态
					TagCaliber: &model.TagCaliber{
						Param: model.Param{
							IntervalPeriod: 3600, // 1小时
						},
						Threshold: model.Threshold{
							DeviceLoginOpenIDNumbers: 3, // 阈值为3个用户
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,  // 10分钟
							ResultScore: 50,   // 50分
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1001,
			ModelCode: "test_model",
			Nid:       "test_user_001",
			Doid:      "test_device_001",
			Ip:        "*************",
			Now:       time.Now(),
		}

		ctx := context.Background()
		
		// 清理测试数据
		scoreKey := collect.ScoreKey(ctx, param)
		recordKey := collect.RecordKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		var scoreItems = make([]*ScoreItem, 0)

		Convey("当设备关联用户数未达到阈值时", func() {
			// 第一次计算，用户数为1，未达到阈值3
			err := collect.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)
			So(len(scoreItems), ShouldEqual, 0)

			// 验证没有评分结果
			_, err = database.GetCaclRdb().Get(ctx, scoreKey).Result()
			So(err, ShouldNotBeNil) // 应该是 redis.Nil 错误
		})

		Convey("当设备关联用户数达到阈值时", func() {
			// 模拟多个用户使用同一设备
			for i := 1; i <= 3; i++ {
				param.Nid = "test_user_" + string(rune('0'+i))
				err := collect.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 第3个用户时应该触发评分
			So(len(scoreItems), ShouldBeGreaterThan, 0)
			So(scoreItems[len(scoreItems)-1].Score, ShouldEqual, 50)
			So(scoreItems[len(scoreItems)-1].Value, ShouldEqual, "1")

			// 验证评分结果已存储
			result, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
			So(err, ShouldBeNil)
			So(strings.HasPrefix(result, "50"), ShouldBeTrue)
		})

		Convey("当参数无效时", func() {
			// 测试空的 Nid
			invalidParam := &BaseParam{
				ClientId:  1001,
				ModelCode: "test_model",
				Nid:       "", // 空的 Nid
				Doid:      "test_device_001",
				Now:       time.Now(),
			}

			err := collect.CalcScore(ctx, invalidParam, &scoreItems)
			So(err, ShouldBeNil) // 应该正常返回，但不计算
		})
	})
}

func TestCollectEventRecord(t *testing.T) {
	Convey("TestCollectEventRecord", t, func() {
		collect := &Collect{
			&DeviceCollect{
				RiskTag: &model.RiskTag{
					Id:        101,
					ClientId:  1001,
					ModelId:   "test_model",
				},
			},
		}

		param := &BaseParam{
			ClientId:  1001,
			ModelCode: "test_model",
			Nid:       "test_user_001",
			Doid:      "test_device_001",
			Now:       time.Now(),
		}

		ctx := context.Background()

		// EventRecord 对于 Collect 类型应该什么都不做
		err := collect.EventRecord(ctx, param)
		So(err, ShouldBeNil)
	})
}

func TestCollectUseRedis(t *testing.T) {
	Convey("TestCollectUseRedis", t, func() {
		collect := &Collect{
			&DeviceCollect{
				RiskTag: &model.RiskTag{
					Id:        102,
					ClientId:  1001,
					ModelId:   "test_model",
				},
			},
		}

		param := &BaseParam{
			ClientId:  1001,
			ModelCode: "test_model",
			Nid:       "test_user_001",
			Doid:      "test_device_001",
			Now:       time.Now(),
		}

		ctx := context.Background()
		window := time.Hour
		threshold := int64(2)

		// 清理测试数据
		recordKey := collect.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, recordKey)

		Convey("第一次调用应该返回false", func() {
			result, err := collect.UseRedis(ctx, param, window, threshold)
			So(err, ShouldBeNil)
			So(result, ShouldBeFalse)
		})

		Convey("达到阈值时应该返回true", func() {
			// 添加足够的成员达到阈值
			for i := 1; i <= int(threshold); i++ {
				param.Nid = "test_user_" + string(rune('0'+i))
				result, err := collect.UseRedis(ctx, param, window, threshold)
				So(err, ShouldBeNil)
				if i == int(threshold) {
					So(result, ShouldBeTrue)
				}
			}
		})
	})
}

func TestCollectWithDifferentTags(t *testing.T) {
	Convey("TestCollectWithDifferentTags", t, func() {
		Convey("测试 IpCollect", func() {
			collect := &Collect{
				&IpCollect{
					RiskTag: &model.RiskTag{
						Id:        103,
						ClientId:  1001,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Param: model.Param{
								IntervalPeriod: 3600,
							},
							Threshold: model.Threshold{
								DeviceLoginOpenIDNumbers: 2,
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 30,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1001,
				ModelCode: "test_model",
				Nid:       "test_user_001",
				Ip:        "*************",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := collect.ScoreKey(ctx, param)
			recordKey := collect.RecordKey(ctx, param)
			database.GetCaclRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 模拟多个用户使用同一IP
			for i := 1; i <= 3; i++ {
				param.Nid = "test_user_" + string(rune('0'+i))
				err := collect.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试 DeviceRoleCollect", func() {
			collect := &Collect{
				&DeviceRoleCollect{
					RiskTag: &model.RiskTag{
						Id:        104,
						ClientId:  1001,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Param: model.Param{
								IntervalPeriod: 3600,
							},
							Threshold: model.Threshold{
								DeviceLoginOpenIDNumbers: 2,
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 40,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1001,
				ModelCode: "test_model",
				RoleId:    "test_role_001",
				Doid:      "test_device_001",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := collect.ScoreKey(ctx, param)
			recordKey := collect.RecordKey(ctx, param)
			database.GetCaclRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 模拟多个角色使用同一设备
			for i := 1; i <= 3; i++ {
				param.RoleId = "test_role_" + string(rune('0'+i))
				err := collect.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})
	})
}
