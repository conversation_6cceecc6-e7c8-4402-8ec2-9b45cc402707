package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestAccountFrequencyUseRedis(t *testing.T) {
	tag := &model.RiskTag{
		Id:        22,
		ClientId:  1025,
		AppId:     "RiskTag22",
		ModelId:   "test_model",
		Name:      "RiskTag22",
		Code:      "RiskTag22",
		Status:    1,
		TagUnit:   1, // '标签值单位 1 bool 2number ',
		RiskType:  1, // '风险属性 1 增分 2 减分',
		ScoreType: 1, // '分值设定 1 静态 2 动态'
		TagCaliber: &model.TagCaliber{
			Param: model.Param{
				IntervalPeriod: 60,
			},
			Threshold: model.Threshold{
				LoginTimes: 2,
			},
		},
		ScoreRange: []model.ScoreRange{
			{
				Period:      600,
				ResultScore: 10,
			},
		},
	}
	a := Frequency{
		&AccountFrequency{tag},
	}
	Convey("AccountFrequency", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			Now:       time.Now(),
		}
		ctx := context.Background()
		key := model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
			a.GetTag().Id, proto.IdKind_NId, param.Nid)

		database.GetRdb().Del(ctx, key)
		database.GetCaclRdb().Del(ctx, a.ScoreKey(ctx, param))

		var ret = make([]*ScoreItem, 0, 20)

		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)
		n, _ := database.GetRdb().ZCard(ctx, key).Result()
		So(n, ShouldEqual, 1)
		err = a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)
		n, _ = database.GetRdb().ZCard(ctx, key).Result()
		So(n, ShouldEqual, 2)

		r, err := database.GetCaclRdb().Get(ctx, a.ScoreKey(ctx, param)).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)

		for i := 0; i < 10; i++ {
			a.CalcScore(ctx, param, &ret)
		}
		n, _ = database.GetRdb().ZCard(ctx, key).Result()
		So(n, ShouldEqual, 2)
	})
}

func TestAccountFrequencyUseClickhouse(t *testing.T) {
	tag := &model.RiskTag{
		Id:        22,
		ClientId:  1025,
		AppId:     "RiskTag22",
		ModelId:   "test_model",
		Name:      "RiskTag22",
		Code:      "RiskTag22",
		Status:    1,
		TagUnit:   1, // '标签值单位 1 bool 2number ',
		RiskType:  1, // '风险属性 1 增分 2 减分',
		ScoreType: 1, // '分值设定 1 静态 2 动态'
		TagCaliber: &model.TagCaliber{
			Param: model.Param{
				IntervalPeriod: 60,
			},
			Threshold: model.Threshold{
				LoginTimes: 300,
			},
		},
		ScoreRange: []model.ScoreRange{
			{
				Period:      600,
				ResultScore: 10,
			},
		},
	}
	a := Frequency{
		&AccountFrequency{tag},
	}
	Convey("AccountFrequency", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			Now:       time.Now(),
		}
		ctx := context.Background()
		key := model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
			a.GetTag().Id, proto.IdKind_NId, param.Nid)
		database.GetRdb().Del(ctx, key)
		database.GetRdb().Del(ctx, a.ScoreKey(ctx, param))
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetRdb().Get(ctx, a.ScoreKey(ctx, param)).Result()
		So(err, ShouldEqual, xredis.Nil)
		So(r, ShouldEqual, "")
		// So(strings.HasPrefix(r, "10"), ShouldEqual, true)

	})
}
