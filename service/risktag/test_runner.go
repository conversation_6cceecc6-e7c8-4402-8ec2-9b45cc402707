package risktag

import (
	"context"
	"fmt"
	"score/model"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

// TestRunner 提供测试运行和验证的辅助功能
type TestRunner struct {
	ctx    context.Context
	params map[string]*BaseParam
}

// NewTestRunner 创建新的测试运行器
func NewTestRunner() *TestRunner {
	return &TestRunner{
		ctx:    context.Background(),
		params: make(map[string]*BaseParam),
	}
}

// CreateBaseParam 创建基础测试参数
func (tr *TestRunner) CreateBaseParam(name string, clientId int64, modelCode string) *BaseParam {
	param := &BaseParam{
		ClientId:  uint32(clientId),
		ModelCode: modelCode,
		Nid:       fmt.Sprintf("test_user_%s", name),
		Doid:      fmt.Sprintf("test_device_%s", name),
		RoleId:    fmt.Sprintf("test_role_%s", name),
		Ip:        fmt.Sprintf("192.168.1.%d", int(clientId%255)),
		Province:  "TestProvince",
		Now:       time.Now(),
	}
	tr.params[name] = param
	return param
}

// GetParam 获取已创建的参数
func (tr *TestRunner) GetParam(name string) *BaseParam {
	return tr.params[name]
}

// CreateFrequencyTag 创建频率标签
func (tr *TestRunner) CreateFrequencyTag(id uint64, clientId int64, threshold uint64, score int64) *Frequency {
	return &Frequency{
		&DeviceFrequency{
			RiskTag: &model.RiskTag{
				Id:       id,
				ClientId: clientId,
				ModelId:  "test_model",
				Code:     fmt.Sprintf("test_freq_%d", id),
				TagCaliber: &model.TagCaliber{
					Param:     model.Param{IntervalPeriod: 60},
					Threshold: model.Threshold{LoginTimes: int64(threshold)},
				},
				ScoreRange: []model.ScoreRange{
					{Period: 300, ResultScore: score},
				},
			},
		},
	}
}

// CreateIntervalTag 创建间隔标签
func (tr *TestRunner) CreateIntervalTag(id uint64, clientId int64, intervalMs int64, score int64) *Interval {
	return &Interval{
		&DeviceInterval{
			RiskTag: &model.RiskTag{
				Id:       id,
				ClientId: clientId,
				ModelId:  "test_model",
				Code:     fmt.Sprintf("test_interval_%d", id),
				TagCaliber: &model.TagCaliber{
					Threshold: model.Threshold{RequestIntervalPeriod: intervalMs},
				},
				ScoreRange: []model.ScoreRange{
					{Period: 300, ResultScore: score},
				},
			},
		},
	}
}

// CreateCollectTag 创建聚集标签
func (tr *TestRunner) CreateCollectTag(id uint64, clientId int64, threshold int64, score int64) *Collect {
	return &Collect{
		&DeviceCollect{
			RiskTag: &model.RiskTag{
				Id:       id,
				ClientId: clientId,
				ModelId:  "test_model",
				Code:     fmt.Sprintf("test_collect_%d", id),
				TagCaliber: &model.TagCaliber{
					Param:     model.Param{IntervalPeriod: 3600},
					Threshold: model.Threshold{DeviceLoginOpenIDNumbers: threshold},
				},
				ScoreRange: []model.ScoreRange{
					{Period: 300, ResultScore: score},
				},
			},
		},
	}
}

// CreateRiskTag 创建风险标签
func (tr *TestRunner) CreateRiskTag(id uint64, clientId int64, labels []string, score int64) *RiskTag {
	return &RiskTag{
		&DeviceRiskTag{
			RiskTag: &model.RiskTag{
				Id:       id,
				ClientId: clientId,
				ModelId:  "test_model",
				Code:     fmt.Sprintf("test_risk_%d", id),
				TagCaliber: &model.TagCaliber{
					Threshold: model.Threshold{DeviceRiskLabels: labels},
				},
				ScoreRange: []model.ScoreRange{
					{Period: 300, ResultScore: score},
				},
			},
		},
	}
}

// RunScoreCalculation 运行评分计算并返回结果
func (tr *TestRunner) RunScoreCalculation(tag Eventer, param *BaseParam) ([]*ScoreItem, error) {
	var scoreItems = make([]*ScoreItem, 0)
	err := tag.CalcScore(tr.ctx, param, &scoreItems)
	return scoreItems, err
}

// ValidateScoreItems 验证评分结果
func (tr *TestRunner) ValidateScoreItems(scoreItems []*ScoreItem, expectedCount int, expectedScore int64) bool {
	if len(scoreItems) != expectedCount {
		return false
	}

	if expectedCount > 0 && scoreItems[len(scoreItems)-1].Score != expectedScore {
		return false
	}

	return true
}

// TestHelperFunctions 测试辅助函数
func TestHelperFunctions(t *testing.T) {
	Convey("TestHelperFunctions", t, func() {
		runner := NewTestRunner()

		Convey("测试参数创建", func() {
			param := runner.CreateBaseParam("test1", 1001, "model1")
			So(param, ShouldNotBeNil)
			So(param.ClientId, ShouldEqual, 1001)
			So(param.ModelCode, ShouldEqual, "model1")
			So(param.Nid, ShouldEqual, "test_user_test1")

			// 测试参数获取
			retrievedParam := runner.GetParam("test1")
			So(retrievedParam, ShouldEqual, param)
		})

		Convey("测试标签创建", func() {
			// 测试频率标签创建
			freqTag := runner.CreateFrequencyTag(1001, 1001, 3, 50)
			So(freqTag, ShouldNotBeNil)
			So(freqTag.GetTag().Id, ShouldEqual, 1001)
			So(freqTag.GetTag().TagCaliber.Threshold.LoginTimes, ShouldEqual, 3)

			// 测试间隔标签创建
			intervalTag := runner.CreateIntervalTag(1002, 1001, 5000, 60)
			So(intervalTag, ShouldNotBeNil)
			So(intervalTag.GetTag().Id, ShouldEqual, 1002)
			So(intervalTag.GetTag().TagCaliber.Threshold.RequestIntervalPeriod, ShouldEqual, 5000)

			// 测试聚集标签创建
			collectTag := runner.CreateCollectTag(1003, 1001, 2, 70)
			So(collectTag, ShouldNotBeNil)
			So(collectTag.GetTag().Id, ShouldEqual, 1003)
			So(collectTag.GetTag().TagCaliber.Threshold.DeviceLoginOpenIDNumbers, ShouldEqual, 2)

			// 测试风险标签创建
			riskTag := runner.CreateRiskTag(1004, 1001, []string{"high_risk", "fraud"}, 80)
			So(riskTag, ShouldNotBeNil)
			So(riskTag.GetTag().Id, ShouldEqual, 1004)
			So(len(riskTag.GetTag().TagCaliber.Threshold.DeviceRiskLabels), ShouldEqual, 2)
		})

		Convey("测试评分计算", func() {
			param := runner.CreateBaseParam("calc_test", 1001, "model1")
			freqTag := runner.CreateFrequencyTag(2001, 1001, 1, 30) // 阈值为1，容易触发

			scoreItems, err := runner.RunScoreCalculation(freqTag, param)
			So(err, ShouldBeNil)

			// 第一次调用应该触发评分
			isValid := runner.ValidateScoreItems(scoreItems, 1, 30)
			So(isValid, ShouldBeTrue)
		})
	})
}

// BenchmarkRiskTagCalculation 性能基准测试
func BenchmarkRiskTagCalculation(b *testing.B) {
	runner := NewTestRunner()
	param := runner.CreateBaseParam("bench", 1001, "bench_model")
	freqTag := runner.CreateFrequencyTag(3001, 1001, 1000, 10) // 高阈值，避免频繁触发

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var scoreItems = make([]*ScoreItem, 0)
		freqTag.CalcScore(context.Background(), param, &scoreItems)
	}
}

// BenchmarkKeyGeneration 键值生成性能测试
func BenchmarkKeyGeneration(b *testing.B) {
	runner := NewTestRunner()
	param := runner.CreateBaseParam("key_bench", 1001, "key_model")
	freqTag := runner.CreateFrequencyTag(4001, 1001, 10, 20)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		freqTag.ScoreKey(context.Background(), param)
		freqTag.RecordKey(context.Background(), param)
	}
}

// TestConcurrentAccess 并发访问测试
func TestConcurrentAccess(t *testing.T) {
	Convey("TestConcurrentAccess", t, func() {
		runner := NewTestRunner()
		freqTag := runner.CreateFrequencyTag(5001, 1001, 100, 25) // 高阈值

		Convey("测试并发计算", func() {
			done := make(chan bool, 50)

			for i := 0; i < 50; i++ {
				go func(index int) {
					defer func() { done <- true }()

					param := runner.CreateBaseParam(fmt.Sprintf("concurrent_%d", index), 1001, "concurrent_model")
					scoreItems, err := runner.RunScoreCalculation(freqTag, param)

					So(err, ShouldBeNil)
					So(scoreItems, ShouldNotBeNil)
				}(i)
			}

			// 等待所有协程完成
			for i := 0; i < 50; i++ {
				<-done
			}
		})
	})
}

// TestEdgeCases 边界情况测试
func TestEdgeCases(t *testing.T) {
	Convey("TestEdgeCases", t, func() {
		runner := NewTestRunner()

		Convey("测试零值参数", func() {
			param := &BaseParam{} // 零值参数
			freqTag := runner.CreateFrequencyTag(6001, 1001, 1, 10)

			scoreItems, err := runner.RunScoreCalculation(freqTag, param)
			So(err, ShouldBeNil)
			So(scoreItems, ShouldNotBeNil)
		})

		Convey("测试极大值", func() {
			param := runner.CreateBaseParam("large_values", 999999999, "large_model")
			freqTag := runner.CreateFrequencyTag(6002, 999999999, 999999, 999999)

			scoreItems, err := runner.RunScoreCalculation(freqTag, param)
			So(err, ShouldBeNil)
			So(scoreItems, ShouldNotBeNil)
		})

		Convey("测试负值处理", func() {
			// 注意：这里测试系统如何处理不合理的配置
			param := runner.CreateBaseParam("negative", 1001, "negative_model")

			// 创建一个配置不合理的标签
			freqTag := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       6003,
						ClientId: 1001,
						ModelId:  "negative_model",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: -1}, // 负值
							Threshold: model.Threshold{LoginTimes: 0},  // 零值
						},
						ScoreRange: []model.ScoreRange{
							{Period: -100, ResultScore: -50}, // 负值
						},
					},
				},
			}

			scoreItems, err := runner.RunScoreCalculation(freqTag, param)
			// 系统应该能够处理这些边界情况
			So(err, ShouldBeNil)
			So(scoreItems, ShouldNotBeNil)
		})
	})
}
