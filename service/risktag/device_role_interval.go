package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DeviceRoleInterval struct {
	*model.RiskTag
}

func (e *DeviceRoleInterval) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return param.Doid != ""
}

func (e *DeviceRoleInterval) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceRoleInterval) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *DeviceRoleInterval) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetFrequencyKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_DoId, param.Doid)
}
