package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type DeviceRegular struct {
	*model.RiskTag
}

func (e *DeviceRegular) EventRecord(ctx context.Context, base *BaseParam) error {
	return nil
}

func (e *DeviceRegular) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceRegular) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetDeviceRegularScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *DeviceRegular) CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error {
	if param.Doid == "" {
		return nil
	}
	// threshold, err := e.RiskTag.GetThresholdScore()
	// if err != nil {
	// 	return err
	// }
	// var percent = threshold.LoginProvincePercentage
	// var num = threshold.ProvinceSuccessiveLogin
	// if percent == 0 || num == 0 {
	// 	return nil
	// }
	// window, err := e.RiskTag.GetWindow()
	// if err != nil {
	// 	return err
	// }
	// var ret1, ret2 bool
	// total, n, err := model.QueryAccountRegion(ctx, param.ClientId, param.ModelCode,
	// 	param.Nid, param.Province, param.Now.Add(-window), param.Now)
	// if err != nil {
	// 	return err
	// }
	// //加上本次
	// n++
	// if percent > 0 && percent >= float64(n+1/total) {
	// 	ret1 = true
	// }
	// if num > 0 && n >= num {
	// 	ret2 = true
	// }
	// key := e.ScoreKey(ctx, param)
	// if ret2 || ret1 {
	// 	s, ttl, err := e.GetBoolScoreAndTLL()
	// 	if err != nil {
	// 		return err
	// 	}
	// 	if s > 0 && ttl > 0 {
	// 		*sItems = append(*sItems, &ScoreItem{
	// 			Value: "1",
	// 			Score: s,
	// 			TTL:   ttl,
	// 			Tag:   e.GetTag(),
	// 		})
	// 		return model.SetScore(ctx, key, s, ttl, "1")
	// 	}
	// }
	return nil
}
