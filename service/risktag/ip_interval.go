package risktag

import (
	"context"
	"score/model"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

type IpInterval struct {
	*model.RiskTag
}

func (e *IpInterval) NeedRecord(ctx context.Context, param *BaseParam) bool {
	return true
}

func (e *IpInterval) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_Ip, param.Ip)
}

func (e *IpInterval) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpInterval) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetIntervalScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}
