package risktag

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xstring"
	"score/model"
	"strings"
)

type RiskTag struct {
	BaseTag
}

func (e *RiskTag) EventRecord(ctx context.Context, base *BaseParam) error {
	return nil
}

func (e *RiskTag) CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error {
	if param.Doid == "" {
		return nil
	}
	threshold, err := e.GetTag().GetThresholdScore()
	if err != nil {
		return err
	}
	var l = threshold.DeviceRiskLabels
	if len(l) == 0 {
		return nil
	}
	tags, err := model.GetRiskDeviceAbnormalTagByDoid(param.Doid)
	if err != nil {
		return err
	}
	if len(tags) == 0 {
		return nil
	}
	var allLabels [][]string
	for _, label := range l {
		p := strings.Split(label, ",")
		allLabels = append(allLabels, p)
	}
	var ret bool
	for _, label := range allLabels {
		if ArrayContains(tags, label) {
			ret = true
			break
		}
	}
	key := e.Score<PERSON>ey(ctx, param)
	if ret {
		s, ttl, err := e.GetTag().GetBoolScoreAndTLL()
		if err != nil {
			return err
		}
		if s > 0 && ttl > 0 {
			*sItems = append(*sItems, &ScoreItem{
				Value: "1",
				Score: s,
				TTL:   ttl,
				Tag:   e.GetTag(),
			})
			return model.SetScore(ctx, key, s, ttl, "1")
		}
	}
	return nil
}

func ArrayContains(arr []string, str []string) bool {
	for _, s := range str {
		if !xstring.Contains(arr, s) {
			return false
		}
	}
	return true
}
