package risktag

import (
	"context"
	"strings"
	"testing"
	"time"

	"score/database"
	"score/model"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestDeviceDoidExceptioNidTag(t *testing.T) {
	a := DeviceDoidException{
		&DoidExceptionNid{
			&model.RiskTag{
				Id:        22,
				ClientId:  1025,
				AppId:     "RiskTag22",
				ModelId:   "test_model",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 60,
					},
					Threshold: model.Threshold{
						DeviceRiskLabels: []string{"device_doid_exception_empty", "device_doid_exception_format_invaid"},
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("doid is empty", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			Doid:      "",
			Now:       time.Now(),
		}
		ctx := context.Background()

		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})

	Convey("format invaid", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			Doid:      "aaaa",
			Now:       time.Now(),
		}
		ctx := context.Background()

		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})
}

func TestDeviceDoidExceptioRoleTag(t *testing.T) {
	a := DeviceDoidException{
		&DoidExceptionRole{
			&model.RiskTag{
				Id:        22,
				ClientId:  1025,
				AppId:     "RiskTag22",
				ModelId:   "test_model",
				Name:      "RiskTag22",
				Code:      "RiskTag22",
				Status:    1,
				TagUnit:   1, // '标签值单位 1 bool 2number ',
				RiskType:  1, // '风险属性 1 增分 2 减分',
				ScoreType: 1, // '分值设定 1 静态 2 动态'
				TagCaliber: &model.TagCaliber{
					Param: model.Param{
						IntervalPeriod: 60,
					},
					Threshold: model.Threshold{
						DeviceRiskLabels: []string{"device_doid_exception_empty", "device_doid_exception_format_invaid"},
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      600,
						ResultScore: 10,
					},
				},
			},
		},
	}
	Convey("doid is empty", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			RoleId:    "98234",
			Doid:      "",
			Now:       time.Now(),
		}
		ctx := context.Background()

		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})

	Convey("format invaid", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			RoleId:    "847584",
			Doid:      "aaaa",
			Now:       time.Now(),
		}
		ctx := context.Background()

		scoreKey := a.ScoreKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		var ret = make([]*ScoreItem, 0, 20)
		err := a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})
}
