package risktag

import (
	"context"
	"score/database"
	"score/model"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestRiskTagIntegrationSuite(t *testing.T) {
	Convey("RiskTag Integration Test Suite", t, func() {
		ctx := context.Background()
		baseTime := time.Now()

		// 通用测试参数
		baseParam := &BaseParam{
			ClientId:  9999,
			ModelCode: "integration_test",
			Nid:       "integration_user",
			Doid:      "integration_device",
			RoleId:    "integration_role",
			Ip:        "*************",
			Province:  "TestProvince",
			Now:       baseTime,
		}

		Convey("测试多种风险标签的组合使用", func() {
			// 创建多种类型的风险标签
			frequency := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       8001,
						ClientId: 9999,
						ModelId:  "integration_test",
						Code:     "integration_frequency",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: 60},
							Threshold: model.Threshold{LoginTimes: 2},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 30}},
					},
				},
			}

			interval := &Interval{
				&DeviceInterval{
					RiskTag: &model.RiskTag{
						Id:       8002,
						ClientId: 9999,
						ModelId:  "integration_test",
						Code:     "integration_interval",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{RequestIntervalPeriod: 3000},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 40}},
					},
				},
			}

			collect := &Collect{
				&DeviceCollect{
					RiskTag: &model.RiskTag{
						Id:       8003,
						ClientId: 9999,
						ModelId:  "integration_test",
						Code:     "integration_collect",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: 3600},
							Threshold: model.Threshold{DeviceLoginOpenIDNumbers: 2},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 50}},
					},
				},
			}

			// 清理测试数据
			cleanupKeys := []string{
				frequency.ScoreKey(ctx, baseParam),
				frequency.RecordKey(ctx, baseParam),
				interval.ScoreKey(ctx, baseParam),
				interval.RecordKey(ctx, baseParam),
				collect.ScoreKey(ctx, baseParam),
				collect.RecordKey(ctx, baseParam),
			}
			for _, key := range cleanupKeys {
				if key != "" {
					database.GetRdb().Del(ctx, key)
					database.GetCaclRdb().Del(ctx, key)
				}
			}

			var allScoreItems = make([]*ScoreItem, 0)

			// 模拟风险行为序列
			Convey("模拟连续的风险行为", func() {
				// 1. 先记录间隔时间
				err := interval.EventRecord(ctx, baseParam)
				So(err, ShouldBeNil)

				// 2. 模拟频繁操作
				for i := 0; i < 3; i++ {
					// 频率标签计算
					err = frequency.CalcScore(ctx, baseParam, &allScoreItems)
					So(err, ShouldBeNil)

					// 短时间间隔操作
					baseParam.Now = baseParam.Now.Add(1 * time.Second)
					err = interval.CalcScore(ctx, baseParam, &allScoreItems)
					So(err, ShouldBeNil)

					// 设备聚集操作
					baseParam.Nid = "integration_user_" + string(rune('1'+i))
					err = collect.CalcScore(ctx, baseParam, &allScoreItems)
					So(err, ShouldBeNil)
				}

				// 验证评分结果
				So(len(allScoreItems), ShouldBeGreaterThan, 0)

				// 统计不同类型的评分
				var frequencyScores, intervalScores, collectScores int
				for _, item := range allScoreItems {
					switch item.Tag.Code {
					case "integration_frequency":
						frequencyScores++
					case "integration_interval":
						intervalScores++
					case "integration_collect":
						collectScores++
					}
				}

				// 验证各种类型的评分都被触发
				So(frequencyScores, ShouldBeGreaterThan, 0)
				So(intervalScores, ShouldBeGreaterThan, 0)
				So(collectScores, ShouldBeGreaterThan, 0)
			})
		})

		Convey("测试不同标签类型的键值生成", func() {
			tags := []Eventer{
				&Frequency{&DeviceFrequency{RiskTag: &model.RiskTag{Id: 8010, Code: "test_device_freq"}}},
				&Frequency{&IpFrequency{RiskTag: &model.RiskTag{Id: 8011, Code: "test_ip_freq"}}},
				&Frequency{&RoleFrequency{RiskTag: &model.RiskTag{Id: 8012, Code: "test_role_freq"}}},
				&Interval{&DeviceInterval{RiskTag: &model.RiskTag{Id: 8013, Code: "test_device_interval"}}},
				&Interval{&IpInterval{RiskTag: &model.RiskTag{Id: 8014, Code: "test_ip_interval"}}},
				&Interval{&RoleInterval{RiskTag: &model.RiskTag{Id: 8015, Code: "test_role_interval"}}},
				&Collect{&DeviceCollect{RiskTag: &model.RiskTag{Id: 8016, Code: "test_device_collect"}}},
				&Collect{&IpCollect{RiskTag: &model.RiskTag{Id: 8017, Code: "test_ip_collect"}}},
			}

			for _, tag := range tags {
				scoreKey := tag.ScoreKey(ctx, baseParam)
				_ = tag.RecordKey(ctx, baseParam) // recordKey 可能为空（某些标签类型不需要记录键）

				So(scoreKey, ShouldNotBeEmpty)

				// 验证键值的唯一性
				So(scoreKey, ShouldContainSubstring, tag.GetTag().Code)
			}
		})

		Convey("测试错误处理和边界情况", func() {
			// 测试无效参数
			invalidParam := &BaseParam{
				ClientId:  0, // 无效的客户端ID
				ModelCode: "",
				Nid:       "",
				Doid:      "",
				Now:       time.Time{}, // 零值时间
			}

			frequency := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       8020,
						ClientId: 9999,
						ModelId:  "integration_test",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: 60},
							Threshold: model.Threshold{LoginTimes: 1},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 10}},
					},
				},
			}

			var scoreItems = make([]*ScoreItem, 0)
			err := frequency.CalcScore(ctx, invalidParam, &scoreItems)
			// 应该能够处理无效参数而不崩溃
			So(err, ShouldBeNil)
		})

		Convey("测试并发安全性", func() {
			frequency := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       8030,
						ClientId: 9999,
						ModelId:  "integration_test",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: 60},
							Threshold: model.Threshold{LoginTimes: 5},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 20}},
					},
				},
			}

			// 清理测试数据
			scoreKey := frequency.ScoreKey(ctx, baseParam)
			recordKey := frequency.RecordKey(ctx, baseParam)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 模拟并发调用
			done := make(chan bool, 10)
			for i := 0; i < 10; i++ {
				go func(index int) {
					defer func() { done <- true }()

					param := &BaseParam{
						ClientId:  9999,
						ModelCode: "integration_test",
						Nid:       "concurrent_user_" + string(rune('0'+index)),
						Doid:      "integration_device",
						Now:       time.Now(),
					}

					var scoreItems = make([]*ScoreItem, 0)
					err := frequency.CalcScore(ctx, param, &scoreItems)
					So(err, ShouldBeNil)
				}(i)
			}

			// 等待所有协程完成
			for i := 0; i < 10; i++ {
				<-done
			}
		})

		Convey("测试配置变更的影响", func() {
			frequency := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       8040,
						ClientId: 9999,
						ModelId:  "integration_test",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: 60},
							Threshold: model.Threshold{LoginTimes: 2},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 25}},
					},
				},
			}

			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := frequency.ScoreKey(ctx, baseParam)
			recordKey := frequency.RecordKey(ctx, baseParam)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 第一次计算
			err := frequency.CalcScore(ctx, baseParam, &scoreItems)
			So(err, ShouldBeNil)

			// 修改配置
			frequency.GetTag().TagCaliber.Threshold.LoginTimes = 1

			// 第二次计算，应该触发评分
			err = frequency.CalcScore(ctx, baseParam, &scoreItems)
			So(err, ShouldBeNil)

			// 验证配置变更生效
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})
	})
}

func TestRiskTagPerformance(t *testing.T) {
	Convey("RiskTag Performance Test", t, func() {
		ctx := context.Background()

		frequency := &Frequency{
			&DeviceFrequency{
				RiskTag: &model.RiskTag{
					Id:       9001,
					ClientId: 9999,
					ModelId:  "performance_test",
					TagCaliber: &model.TagCaliber{
						Param:     model.Param{IntervalPeriod: 60},
						Threshold: model.Threshold{LoginTimes: 100}, // 高阈值，避免频繁触发
					},
					ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 10}},
				},
			},
		}

		param := &BaseParam{
			ClientId:  9999,
			ModelCode: "performance_test",
			Nid:       "perf_user",
			Doid:      "perf_device",
			Now:       time.Now(),
		}

		// 清理测试数据
		scoreKey := frequency.ScoreKey(ctx, param)
		recordKey := frequency.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("测试大量计算的性能", func() {
			start := time.Now()
			iterations := 1000

			for i := 0; i < iterations; i++ {
				var scoreItems = make([]*ScoreItem, 0)
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			duration := time.Since(start)
			avgDuration := duration / time.Duration(iterations)

			// 验证平均每次计算时间应该在合理范围内（比如小于10ms）
			So(avgDuration, ShouldBeLessThan, 10*time.Millisecond)
		})
	})
}

func TestRiskTagMemoryUsage(t *testing.T) {
	Convey("RiskTag Memory Usage Test", t, func() {
		ctx := context.Background()

		// 创建大量的风险标签实例
		var tags []Eventer
		for i := 0; i < 100; i++ {
			tag := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       uint64(10000 + i),
						ClientId: 9999,
						ModelId:  "memory_test",
						TagCaliber: &model.TagCaliber{
							Param:     model.Param{IntervalPeriod: 60},
							Threshold: model.Threshold{LoginTimes: 10},
						},
						ScoreRange: []model.ScoreRange{{Period: 300, ResultScore: 5}},
					},
				},
			}
			tags = append(tags, tag)
		}

		param := &BaseParam{
			ClientId:  9999,
			ModelCode: "memory_test",
			Nid:       "memory_user",
			Doid:      "memory_device",
			Now:       time.Now(),
		}

		Convey("测试内存使用情况", func() {
			// 执行计算
			for _, tag := range tags {
				var scoreItems = make([]*ScoreItem, 0)
				err := tag.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 验证标签数量
			So(len(tags), ShouldEqual, 100)

			// 清理资源
			tags = nil
		})
	})
}
