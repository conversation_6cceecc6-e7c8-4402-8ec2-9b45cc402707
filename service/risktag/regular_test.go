package risktag

import (
	"context"
	"score/model"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

func TestDeviceRegular(t *testing.T) {
	Convey("TestDeviceRegular", t, func() {
		deviceRegular := &DeviceRegular{
			RiskTag: &model.RiskTag{
				Id:        700,
				ClientId:  1007,
				AppId:     "test_app",
				ModelId:   "test_model",
				Name:      "TestDeviceRegular",
				Code:      "test_device_regular",
				Status:    1,
				TagUnit:   1,
				RiskType:  1,
				ScoreType: 1,
				TagCaliber: &model.TagCaliber{
					Threshold: model.Threshold{
						LoginProvincePercentage:  0.8, // 80%
						ProvinceSuccessiveLogin:  5,   // 5次
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      1800,
						ResultScore: 60,
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1007,
			ModelCode: "test_model",
			Nid:       "test_user_007",
			Doid:      "test_device_007",
			Province:  "Beijing",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		Convey("当设备ID为空时", func() {
			paramWithoutDoid := &BaseParam{
				ClientId:  1007,
				ModelCode: "test_model",
				Nid:       "test_user_007",
				Doid:      "", // 空的设备ID
				Province:  "Beijing",
				Now:       time.Now(),
			}

			err := deviceRegular.CalcScore(ctx, paramWithoutDoid, &scoreItems)
			So(err, ShouldBeNil)
			
			// 应该直接返回，不进行计算
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("当设备ID不为空时", func() {
			err := deviceRegular.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)
			
			// 由于实现被注释掉了，应该直接返回
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("测试GetTag方法", func() {
			tag := deviceRegular.GetTag()
			So(tag, ShouldEqual, deviceRegular.RiskTag)
			So(tag.Id, ShouldEqual, 700)
			So(tag.Name, ShouldEqual, "TestDeviceRegular")
		})

		Convey("测试ScoreKey方法", func() {
			scoreKey := deviceRegular.ScoreKey(ctx, param)
			So(scoreKey, ShouldNotBeEmpty)
			
			// 验证键值包含正确的参数
			// 由于 model.GetDeviceRegularScoreKey 的具体实现未知，
			// 我们只能验证返回值不为空
		})

		Convey("测试EventRecord方法", func() {
			err := deviceRegular.EventRecord(ctx, param)
			So(err, ShouldBeNil)
			
			// EventRecord 应该什么都不做
		})
	})
}

func TestIpRegular(t *testing.T) {
	Convey("TestIpRegular", t, func() {
		ipRegular := &IpRegular{
			RiskTag: &model.RiskTag{
				Id:        701,
				ClientId:  1007,
				AppId:     "test_app",
				ModelId:   "test_model",
				Name:      "TestIpRegular",
				Code:      "test_ip_regular",
				Status:    1,
				TagUnit:   1,
				RiskType:  1,
				ScoreType: 1,
				TagCaliber: &model.TagCaliber{
					Threshold: model.Threshold{
						LoginProvincePercentage:  0.7, // 70%
						ProvinceSuccessiveLogin:  3,   // 3次
					},
				},
				ScoreRange: []model.ScoreRange{
					{
						Period:      1200,
						ResultScore: 50,
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1007,
			ModelCode: "test_model",
			Nid:       "test_user_007",
			Ip:        "192.168.1.700",
			Province:  "Shanghai",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		Convey("当IP为空时", func() {
			paramWithoutIp := &BaseParam{
				ClientId:  1007,
				ModelCode: "test_model",
				Nid:       "test_user_007",
				Ip:        "", // 空的IP
				Province:  "Shanghai",
				Now:       time.Now(),
			}

			err := ipRegular.CalcScore(ctx, paramWithoutIp, &scoreItems)
			So(err, ShouldBeNil)
			
			// 应该直接返回，不进行计算
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("当IP不为空时", func() {
			err := ipRegular.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)
			
			// 由于实现被注释掉了，应该直接返回
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("测试GetTag方法", func() {
			tag := ipRegular.GetTag()
			So(tag, ShouldEqual, ipRegular.RiskTag)
			So(tag.Id, ShouldEqual, 701)
			So(tag.Name, ShouldEqual, "TestIpRegular")
		})

		Convey("测试ScoreKey方法", func() {
			scoreKey := ipRegular.ScoreKey(ctx, param)
			So(scoreKey, ShouldNotBeEmpty)
			
			// 验证键值包含正确的参数
			// 由于 model.GetIpRegularScoreKey 的具体实现未知，
			// 我们只能验证返回值不为空
		})

		Convey("测试EventRecord方法", func() {
			err := ipRegular.EventRecord(ctx, param)
			So(err, ShouldBeNil)
			
			// EventRecord 应该什么都不做
		})
	})
}

func TestRegularIntegration(t *testing.T) {
	Convey("TestRegularIntegration", t, func() {
		Convey("测试DeviceRegular的完整流程", func() {
			deviceRegular := &DeviceRegular{
				RiskTag: &model.RiskTag{
					Id:        702,
					ClientId:  1007,
					ModelId:   "test_model",
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							LoginProvincePercentage:  0.9,
							ProvinceSuccessiveLogin:  10,
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      3600,
							ResultScore: 80,
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1007,
				ModelCode: "test_model",
				Nid:       "test_user_007",
				Doid:      "test_device_007",
				Province:  "Guangdong",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 测试完整的计算流程
			err := deviceRegular.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 验证阈值配置
			threshold, err := deviceRegular.GetTag().GetThresholdScore()
			So(err, ShouldBeNil)
			So(threshold.LoginProvincePercentage, ShouldEqual, 0.9)
			So(threshold.ProvinceSuccessiveLogin, ShouldEqual, 10)

			// 测试ScoreKey生成
			scoreKey := deviceRegular.ScoreKey(ctx, param)
			So(scoreKey, ShouldNotBeEmpty)
		})

		Convey("测试IpRegular的完整流程", func() {
			ipRegular := &IpRegular{
				RiskTag: &model.RiskTag{
					Id:        703,
					ClientId:  1007,
					ModelId:   "test_model",
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							LoginProvincePercentage:  0.6,
							ProvinceSuccessiveLogin:  8,
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      2400,
							ResultScore: 70,
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1007,
				ModelCode: "test_model",
				Nid:       "test_user_007",
				Ip:        "192.168.1.703",
				Province:  "Zhejiang",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 测试完整的计算流程
			err := ipRegular.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 验证阈值配置
			threshold, err := ipRegular.GetTag().GetThresholdScore()
			So(err, ShouldBeNil)
			So(threshold.LoginProvincePercentage, ShouldEqual, 0.6)
			So(threshold.ProvinceSuccessiveLogin, ShouldEqual, 8)

			// 测试ScoreKey生成
			scoreKey := ipRegular.ScoreKey(ctx, param)
			So(scoreKey, ShouldNotBeEmpty)
		})
	})
}

func TestRegularErrorCases(t *testing.T) {
	Convey("TestRegularErrorCases", t, func() {
		Convey("测试DeviceRegular的错误情况", func() {
			deviceRegular := &DeviceRegular{
				RiskTag: &model.RiskTag{
					Id:         704,
					ClientId:   1007,
					ModelId:    "test_model",
					TagCaliber: nil, // nil TagCaliber
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,
							ResultScore: 50,
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1007,
				ModelCode: "test_model",
				Nid:       "test_user_007",
				Doid:      "test_device_007",
				Province:  "Beijing",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := deviceRegular.CalcScore(ctx, param, &scoreItems)
			// 由于实现被注释掉了，应该正常返回
			So(err, ShouldBeNil)
		})

		Convey("测试IpRegular的错误情况", func() {
			ipRegular := &IpRegular{
				RiskTag: &model.RiskTag{
					Id:         705,
					ClientId:   1007,
					ModelId:    "test_model",
					TagCaliber: nil, // nil TagCaliber
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,
							ResultScore: 50,
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1007,
				ModelCode: "test_model",
				Nid:       "test_user_007",
				Ip:        "192.168.1.705",
				Province:  "Shanghai",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := ipRegular.CalcScore(ctx, param, &scoreItems)
			// 由于实现被注释掉了，应该正常返回
			So(err, ShouldBeNil)
		})
	})
}

func TestRegularConstants(t *testing.T) {
	Convey("TestRegularConstants", t, func() {
		Convey("测试protobuf常量", func() {
			// 验证protobuf常量可以正常使用
			So(proto.IdKind_NId, ShouldNotBeNil)
			So(proto.IdKind_Ip, ShouldNotBeNil)
		})
	})
}
