package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DeviceRoleCollect struct {
	*model.RiskTag
}

func (e *DeviceRoleCollect) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceRoleCollect) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *DeviceRoleCollect) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_DoId, param.Doid)
}

func (e *DeviceRoleCollect) GetMember(ctx context.Context, param *BaseParam) string {
	return param.RoleId
}

func (e *DeviceRoleCollect) ValidParam(ctx context.Context, param *BaseParam) bool {
	return param.Doid != ""
}
