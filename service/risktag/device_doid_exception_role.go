package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DoidExceptionRole struct {
	*model.RiskTag
}

func (e *DoidExceptionRole) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetDeviceTagScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *DoidExceptionRole) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DoidExceptionRole) RecordKey(ctx context.Context, param *BaseParam) string {
	return ""
}
