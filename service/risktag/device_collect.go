package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DeviceCollect struct {
	*model.RiskTag
}

func (e *DeviceCollect) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_DoId, param.Doid)
}

func (e *DeviceCollect) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_NId, param.Nid)
}

func (e *DeviceCollect) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceCollect) GetMember(ctx context.Context, param *BaseParam) string {
	return param.Nid
}

func (e *DeviceCollect) ValidParam(ctx context.Context, param *BaseParam) bool {
	return param.Doid != ""
}
