package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestRoleInterval(t *testing.T) {
	tag := &model.RiskTag{
		Id:        22,
		ClientId:  1025,
		AppId:     "RiskTag22",
		ModelId:   "test_model",
		Name:      "RiskTag22",
		Code:      "RiskTag22",
		Status:    1,
		TagUnit:   1, // '标签值单位 1 bool 2number ',
		RiskType:  1, // '风险属性 1 增分 2 减分',
		ScoreType: 1, // '分值设定 1 静态 2 动态'
		TagCaliber: &model.TagCaliber{
			Param: model.Param{
				IntervalPeriod: 60,
			},
			Threshold: model.Threshold{
				RequestIntervalPeriod: 200,
			},
		},
		ScoreRange: []model.ScoreRange{
			{
				Period:      600,
				ResultScore: 10,
			},
		},
	}
	a := Interval{
		&AccountInterval{
			RiskTag: tag,
		},
	}
	Convey("AccountInterval", t, func() {
		param := &BaseParam{
			ClientId:  1025,
			ModelCode: "test_model",
			Nid:       "126988",
			Now:       time.Now(),
		}
		ctx := context.Background()
		key := a.RecordKey(ctx, param)
		scoreKey := a.ScoreKey(ctx, param)
		database.GetRdb().Del(ctx, key)
		database.GetCaclRdb().Del(ctx, scoreKey)
		t.Log(key, scoreKey)
		err := a.EventRecord(ctx, param)
		So(err, ShouldEqual, nil)
		var ret = make([]*ScoreItem, 0, 20)
		err = a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		err = a.EventRecord(ctx, param)
		So(err, ShouldEqual, nil)
		err = a.CalcScore(ctx, param, &ret)
		So(err, ShouldEqual, nil)

		r, err := database.GetCaclRdb().Get(ctx, scoreKey).Result()
		So(err, ShouldEqual, nil)
		So(strings.HasPrefix(r, "10"), ShouldEqual, true)
	})
}
