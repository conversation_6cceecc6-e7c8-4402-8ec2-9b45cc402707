package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type DeviceRiskTagForRole struct {
	*model.RiskTag
}

func (e *DeviceRiskTagForRole) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *DeviceRiskTagForRole) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetDeviceTagScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *DeviceRiskTagForRole) RecordKey(ctx context.Context, param *BaseParam) string {
	return ""
}
