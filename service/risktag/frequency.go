package risktag

import (
	"context"
	"score/model"
	"time"
)

type Frequency struct {
	BaseTag
}

func (e *Frequency) EventRecord(ctx context.Context, param *BaseParam) error {
	return nil
}

func (e *Frequency) CalcScore(ctx context.Context, param *BaseParam, sItems *[]*ScoreItem) error {
	window, err := e.GetTag().GetWindow()
	if err != nil {
		return err
	}
	threshold, err := e.GetTag().GetThresholdScore()
	if err != nil {
		return err
	}
	if threshold == nil || threshold.LoginTimes == 0 {
		return nil
	}

	var ret bool
	ret, err = e.UseRedis(ctx, param, window, uint64(threshold.LoginTimes))
	if err != nil {
		return err
	}
	key := e.Score<PERSON>ey(ctx, param)
	if ret {
		s, ttl, err := e.GetTag().GetBoolScoreAndTLL()
		if err != nil {
			return err
		}
		if s > 0 && ttl > 0 {
			*sItems = append(*sItems, &ScoreItem{
				Value: "1",
				Score: s,
				TTL:   ttl,
				Tag:   e.GetTag(),
			})
			return model.SetScore(ctx, key, s, ttl, "1")
		}
	}
	return nil
}

func (e *Frequency) UseRedis(ctx context.Context, param *BaseParam, window time.Duration, loginTimes uint64) (bool, error) {
	key := e.RecordKey(ctx, param)
	return model.WindowRateLimiter(ctx, key, window, loginTimes)
}
