package risktag

import (
	"testing"
)

// TestArrayContainsSimple 简单的数组包含测试，不依赖外部资源
func TestArrayContainsSimple(t *testing.T) {
	tests := []struct {
		name     string
		arr      []string
		str      []string
		expected bool
	}{
		{
			name:     "所有元素都存在",
			arr:      []string{"high_risk", "fraud", "bot", "automation"},
			str:      []string{"high_risk", "fraud"},
			expected: true,
		},
		{
			name:     "部分元素不存在",
			arr:      []string{"high_risk", "fraud"},
			str:      []string{"high_risk", "bot"},
			expected: false,
		},
		{
			name:     "所有元素都不存在",
			arr:      []string{"normal", "safe"},
			str:      []string{"high_risk", "fraud"},
			expected: false,
		},
		{
			name:     "检查空数组",
			arr:      []string{"high_risk", "fraud"},
			str:      []string{},
			expected: true,
		},
		{
			name:     "源数组为空",
			arr:      []string{},
			str:      []string{"high_risk"},
			expected: false,
		},
		{
			name:     "两个数组都为空",
			arr:      []string{},
			str:      []string{},
			expected: true,
		},
		{
			name:     "单个元素匹配",
			arr:      []string{"high_risk", "fraud", "bot"},
			str:      []string{"fraud"},
			expected: true,
		},
		{
			name:     "重复元素",
			arr:      []string{"high_risk", "fraud", "high_risk"},
			str:      []string{"high_risk", "high_risk"},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ArrayContains(tt.arr, tt.str)
			if result != tt.expected {
				t.Errorf("ArrayContains(%v, %v) = %v, expected %v", 
					tt.arr, tt.str, result, tt.expected)
			}
		})
	}
}

// TestExcludeDeviceConstant 测试常量定义
func TestExcludeDeviceConstant(t *testing.T) {
	if ExcludeDevice != "NONE" {
		t.Errorf("ExcludeDevice = %v, expected %v", ExcludeDevice, "NONE")
	}
}

// BenchmarkArrayContains 性能基准测试
func BenchmarkArrayContains(b *testing.B) {
	arr := []string{"high_risk", "fraud", "bot", "automation", "suspicious", "malware"}
	str := []string{"high_risk", "fraud", "bot"}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ArrayContains(arr, str)
	}
}

// BenchmarkArrayContainsLarge 大数组性能测试
func BenchmarkArrayContainsLarge(b *testing.B) {
	// 创建大数组
	arr := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		arr[i] = "item_" + string(rune('0'+(i%10)))
	}
	
	str := []string{"item_1", "item_2", "item_3"}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ArrayContains(arr, str)
	}
}

// TestArrayContainsEdgeCases 边界情况测试
func TestArrayContainsEdgeCases(t *testing.T) {
	// 测试 nil 数组
	var nilArr []string
	var nilStr []string
	
	result := ArrayContains(nilArr, nilStr)
	if !result {
		t.Errorf("ArrayContains(nil, nil) = %v, expected true", result)
	}
	
	result = ArrayContains(nilArr, []string{"test"})
	if result {
		t.Errorf("ArrayContains(nil, [test]) = %v, expected false", result)
	}
	
	// 测试包含空字符串的数组
	arr := []string{"", "test", ""}
	str := []string{""}
	
	result = ArrayContains(arr, str)
	if !result {
		t.Errorf("ArrayContains([, test, ], []) = %v, expected true", result)
	}
	
	// 测试大小写敏感
	arr = []string{"High_Risk", "FRAUD"}
	str = []string{"high_risk", "fraud"}
	
	result = ArrayContains(arr, str)
	if result {
		t.Errorf("ArrayContains([High_Risk, FRAUD], [high_risk, fraud]) = %v, expected false (case sensitive)", result)
	}
}

// TestArrayContainsConcurrency 并发安全测试
func TestArrayContainsConcurrency(t *testing.T) {
	arr := []string{"high_risk", "fraud", "bot", "automation"}
	str := []string{"high_risk", "fraud"}
	
	// 启动多个 goroutine 并发调用
	done := make(chan bool, 100)
	
	for i := 0; i < 100; i++ {
		go func() {
			defer func() { done <- true }()
			
			result := ArrayContains(arr, str)
			if !result {
				t.Errorf("Concurrent ArrayContains failed")
			}
		}()
	}
	
	// 等待所有 goroutine 完成
	for i := 0; i < 100; i++ {
		<-done
	}
}
