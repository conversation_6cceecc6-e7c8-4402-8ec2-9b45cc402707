package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestIntervalCalcScore(t *testing.T) {
	Convey("TestIntervalCalcScore", t, func() {
		// 创建测试用的 Interval 实例
		interval := &Interval{
			&DeviceInterval{
				RiskTag: &model.RiskTag{
					Id:        300,
					ClientId:  1003,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestInterval",
					Code:      "test_interval",
					Status:    1,
					TagUnit:   1, // bool
					RiskType:  1, // 增分
					ScoreType: 1, // 静态
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							RequestIntervalPeriod: 5000, // 5秒间隔阈值（毫秒）
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,  // 10分钟
							ResultScore: 70,   // 70分
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1003,
			ModelCode: "test_model",
			Nid:       "test_user_003",
			Doid:      "test_device_003",
			Now:       time.Now(),
		}

		ctx := context.Background()
		
		// 清理测试数据
		scoreKey := interval.ScoreKey(ctx, param)
		recordKey := interval.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		var scoreItems = make([]*ScoreItem, 0)

		Convey("当没有历史记录时", func() {
			// 第一次调用，没有历史记录
			err := interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)
			
			// 应该没有评分结果
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("当间隔时间正常时", func() {
			// 先记录一次时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 等待足够长的时间（大于阈值）
			param.Now = param.Now.Add(10 * time.Second)
			
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)
			
			// 间隔时间正常，应该没有评分
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("当间隔时间过短时", func() {
			// 先记录一次时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 很短的时间间隔（小于阈值5秒）
			param.Now = param.Now.Add(2 * time.Second)
			
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 间隔时间过短，应该触发评分
			So(len(scoreItems), ShouldBeGreaterThan, 0)
			So(scoreItems[len(scoreItems)-1].Score, ShouldEqual, 70)
			So(scoreItems[len(scoreItems)-1].Value, ShouldEqual, "1")

			// 验证评分结果已存储
			result, err := database.GetRdb().Get(ctx, scoreKey).Result()
			So(err, ShouldBeNil)
			So(strings.HasPrefix(result, "70"), ShouldBeTrue)
		})

		Convey("测试边界情况 - 恰好等于阈值", func() {
			// 清理数据
			database.GetRdb().Del(ctx, recordKey)
			scoreItems = make([]*ScoreItem, 0)

			// 先记录一次时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 恰好等于阈值的时间间隔
			param.Now = param.Now.Add(5 * time.Second)
			
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 恰好等于阈值，应该触发评分
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})
	})
}

func TestIntervalEventRecord(t *testing.T) {
	Convey("TestIntervalEventRecord", t, func() {
		interval := &Interval{
			&DeviceInterval{
				RiskTag: &model.RiskTag{
					Id:        301,
					ClientId:  1003,
					ModelId:   "test_model",
				},
			},
		}

		param := &BaseParam{
			ClientId:  1003,
			ModelCode: "test_model",
			Nid:       "test_user_003",
			Doid:      "test_device_003",
			Now:       time.Now(),
		}

		ctx := context.Background()
		recordKey := interval.RecordKey(ctx, param)

		// 清理测试数据
		database.GetRdb().Del(ctx, recordKey)

		Convey("记录时间戳", func() {
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 验证时间戳已记录
			timestamp, err := model.GetLastTime(ctx, recordKey)
			So(err, ShouldBeNil)
			So(timestamp, ShouldBeGreaterThan, 0)
			
			// 验证记录的时间接近当前时间
			recordedTime := time.UnixMilli(timestamp)
			timeDiff := param.Now.Sub(recordedTime)
			So(timeDiff, ShouldBeLessThan, time.Second) // 误差应该小于1秒
		})

		Convey("测试 NeedRecord 为 false 的情况", func() {
			// 创建一个 NeedRecord 返回 false 的实例
			intervalWithNoRecord := &Interval{
				&IpInterval{
					RiskTag: &model.RiskTag{
						Id:        302,
						ClientId:  1003,
						ModelId:   "test_model",
					},
				},
			}

			// 设置空的 IP 使 NeedRecord 返回 false
			paramWithoutIp := &BaseParam{
				ClientId:  1003,
				ModelCode: "test_model",
				Nid:       "test_user_003",
				Ip:        "", // 空的 IP
				Now:       time.Now(),
			}

			err := intervalWithNoRecord.EventRecord(ctx, paramWithoutIp)
			So(err, ShouldBeNil) // 应该正常返回，但不记录
		})
	})
}

func TestIntervalWithDifferentTags(t *testing.T) {
	Convey("TestIntervalWithDifferentTags", t, func() {
		Convey("测试 IpInterval", func() {
			interval := &Interval{
				&IpInterval{
					RiskTag: &model.RiskTag{
						Id:        303,
						ClientId:  1003,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								RequestIntervalPeriod: 3000, // 3秒
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1003,
				ModelCode: "test_model",
				Nid:       "test_user_003",
				Ip:        "192.168.1.300",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := interval.ScoreKey(ctx, param)
			recordKey := interval.RecordKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 短时间间隔
			param.Now = param.Now.Add(1 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试 RoleInterval", func() {
			interval := &Interval{
				&RoleInterval{
					RiskTag: &model.RiskTag{
						Id:        304,
						ClientId:  1003,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								RequestIntervalPeriod: 4000, // 4秒
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 60,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1003,
				ModelCode: "test_model",
				RoleId:    "test_role_003",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := interval.ScoreKey(ctx, param)
			recordKey := interval.RecordKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 短时间间隔
			param.Now = param.Now.Add(2 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试 AccountInterval", func() {
			interval := &Interval{
				&AccountInterval{
					RiskTag: &model.RiskTag{
						Id:        305,
						ClientId:  1003,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								RequestIntervalPeriod: 2000, // 2秒
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 80,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1003,
				ModelCode: "test_model",
				Nid:       "test_user_003",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := interval.ScoreKey(ctx, param)
			recordKey := interval.RecordKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 短时间间隔
			param.Now = param.Now.Add(1 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})
	})
}

func TestIntervalErrorCases(t *testing.T) {
	Convey("TestIntervalErrorCases", t, func() {
		Convey("测试无效的阈值配置", func() {
			interval := &Interval{
				&DeviceInterval{
					RiskTag: &model.RiskTag{
						Id:        306,
						ClientId:  1003,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								RequestIntervalPeriod: 0, // 无效的阈值
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1003,
				ModelCode: "test_model",
				Nid:       "test_user_003",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 任何时间间隔都应该触发评分（因为阈值为0）
			param.Now = param.Now.Add(1 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试无效的评分配置", func() {
			interval := &Interval{
				&DeviceInterval{
					RiskTag: &model.RiskTag{
						Id:        307,
						ClientId:  1003,
						ModelId:   "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								RequestIntervalPeriod: 5000,
							},
						},
						ScoreRange: []model.ScoreRange{}, // 空的评分范围
					},
				},
			}

			param := &BaseParam{
				ClientId:  1003,
				ModelCode: "test_model",
				Nid:       "test_user_003",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 短时间间隔
			param.Now = param.Now.Add(1 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldNotBeNil) // 应该返回错误
		})
	})
}
