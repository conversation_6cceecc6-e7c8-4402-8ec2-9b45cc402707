package risktag

import (
	"context"
	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"score/model"
)

type IpRoleCollect struct {
	*model.RiskTag
}

func (e *IpRoleCollect) GetTag() *model.RiskTag {
	return e.RiskTag
}

func (e *IpRoleCollect) ScoreKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectScoreKey(ctx, param.ClientId, param.ModelCode, e.RiskTag.Id,
		proto.IdKind_RoleId, param.RoleId)
}

func (e *IpRoleCollect) RecordKey(ctx context.Context, param *BaseParam) string {
	return model.GetCollectKey(ctx, param.ClientId, param.ModelCode,
		e.RiskTag.Id, proto.IdKind_Ip, param.Ip)
}

func (e *IpRoleCollect) GetMember(ctx context.Context, param *BaseParam) string {
	return param.RoleId
}

func (e *IpRoleCollect) ValidParam(ctx context.Context, param *BaseParam) bool {
	return param.Ip != ""
}
