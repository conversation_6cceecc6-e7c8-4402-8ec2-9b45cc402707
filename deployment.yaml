apiVersion: v1
data:
  conf.yaml: |
    sparrow:
      configure:
        provider: "apollo"
        watch: true
        path: "application.yaml"
        config:
          endpoints:
            - "http://10.149.52.104:8080"
          appID: "score"
          cluster: "default"
          namespace: "application"
          secret: "0b8cba36e17541f5bc2b939015d76908"
kind: ConfigMap
metadata:
  name: score
  namespace: platsdk

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: score
  name: score
  namespace: platsdk
spec:
  replicas: 1
  selector:
    matchLabels:
      app: score
  template:
    metadata:
      annotations:
        k8s.aliyun.com/eci-eviction-enable: 'true'
        k8s.aliyun.com/eci-extra-ephemeral-storage: 100G
        prometheus.io/path: /metrics
        prometheus.io/port: '4006'
        prometheus.io/scrape: 'true'
        sidecar.istio.io/inject: 'false'
      labels:
        app: score
    spec:
      containers:
        - env:
            - name: APP_NAME
              value: score
            - name: APP_ENV
              value: dev
            - name: ROLE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['role']
            - name: TZ
              value: Asia/Shanghai
          image: dev-registry-vpc.cn-hangzhou.cr.aliyuncs.com/platform/score:VERSION
          imagePullPolicy: Always
          livenessProbe:
            initialDelaySeconds: 45
            periodSeconds: 12
            tcpSocket:
              port: 8091
            timeoutSeconds: 5
          name: score
          ports:
            - containerPort: 8091
              name: http
              protocol: TCP
          readinessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            tcpSocket:
              port: 8091
            timeoutSeconds: 5
          securityContext: { }
          volumeMounts:
            - mountPath: /data/logs
              name: k8s-log
            - mountPath: /app/conf.yaml
              name: score
              subPath: conf.yaml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: lingyun
      restartPolicy: Always
      securityContext: { }
      serviceAccountName: default
      terminationGracePeriodSeconds: 45
      volumes:
        - emptyDir: { }
          name: k8s-log
        - configMap:
            name: score
          name: score
