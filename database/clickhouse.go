package database

import (
	"context"
	"fmt"
	"strings"

	ch "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type Config struct {
	Addrs        []string
	Database     string
	Username     string
	Password     string
	Debug        bool
	MaxOpenConns int `xconf:"max_open_conns"`
	MaxIdleConns int `xconf:"max_idle_conns"`
}

func StdConfig() *Config {
	return RawConfig("sparrow.database.clickhouse")
}

func RawConfig(key string) *Config {
	config := DefaultConfig()
	if err := xconf.UnmarshalKey(key, config); err != nil {
		xlog.Panic("xgorm.RawConfig with error",
			xlog.String("key", key),
			xlog.Err(err))
	}
	return config
}

func DefaultConfig() *Config {
	return &Config{
		Debug:        false,
		MaxOpenConns: 20,
		MaxIdleConns: 5,
	}
}

func (c *Config) Build() (driver.Conn, error) {
	clickhouse, err := ch.Open(&ch.Options{
		Addr: c.Addrs,
		Auth: ch.Auth{
			Database: c.Database,
			Username: c.Username,
			Password: c.Password,
		},
		Debug:        c.Debug,
		MaxOpenConns: c.MaxOpenConns,
		MaxIdleConns: c.MaxIdleConns,
		Debugf: func(format string, v ...interface{}) {
			str := fmt.Sprintf(format, v...)
			if strings.Contains(str, "send query") {
				xlog.Debug("clickhouse", xlog.String("", fmt.Sprintf(format, v...)))
			}
		},
	})
	if err != nil {
		return nil, err
	}
	if err := clickhouse.Ping(context.TODO()); err != nil {
		if exception, ok := err.(*ch.Exception); ok {
			xlog.Error("clickhouse.Ping with error",
				xlog.Err(fmt.Errorf("exception [%d] %s%s", exception.Code,
					exception.Message, exception.StackTrace)),
			)
		}
		return nil, err
	}
	return clickhouse, nil
}
