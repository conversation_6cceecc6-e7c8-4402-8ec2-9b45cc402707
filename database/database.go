package database

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var sql *xgorm.DB
var rdb *xredis.Client

// var clickhouse driver.Conn
var caclRdb *xredis.Client

func Get() *xgorm.DB             { return sql }
func GetRdb() *xredis.Client     { return rdb }
func GetCaclRdb() *xredis.Client { return caclRdb }

// func GetClickhouse() driver.Conn { return clickhouse }
func Startup() error {
	var err error
	config := xgorm.StdConfig()
	sql, err = config.WithInterceptor(xgorm.TraceInterceptor()).Build()
	if err != nil {
		xlog.Error("database.Startup failed", xlog.Err(err))
		return err
	}
	rdb, err = xredis.StdConfig().Build()
	rdb.AddHook(xredis.TracingHook())
	if err != nil {
		xlog.Error("cache.Startup failed", xlog.Err(err))
		return err
	}
	caclRdb, err = xredis.RawConfig("sparrow.database.calc_redis").Build()
	caclRdb.AddHook(xredis.TracingHook())
	if err != nil {
		xlog.Error("cache.Startup failed", xlog.Err(err))
		return err
	}
	// clickhouse, err = StdConfig().Build()
	// if err != nil {
	// 	xlog.Error("clickhouse.Startup failed", xlog.Err(err))
	// 	return err
	// }
	return nil
}
