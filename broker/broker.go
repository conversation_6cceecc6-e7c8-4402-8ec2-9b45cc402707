package broker

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
)

var reader *xkafka.Reader
var biwriter *xkafka.Writer

func Get() *xkafka.Reader {
	return reader
}

func GetBI() *xkafka.Writer {
	return biwriter
}

func Startup() error {
	config := xkafka.StdConfig()
	var err error
	reader, err = config.BuildReader()
	if err != nil {
		return err
	}
	biConfig := xkafka.RawConfig("sparrow.broker.bikafka")
	biwriter, err = biConfig.BuildWriter()
	if err != nil {
		return err
	}
	return nil
}
