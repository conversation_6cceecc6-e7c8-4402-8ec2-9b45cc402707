package integration

import (
	"fmt"
	"os"
	"sync"
	"testing"
	"time"

	"score/broker"
	"score/config"
	"score/database"
	"score/service"
	"score/worker"

	"gitlab.papegames.com/fringe/sparrow"
)

func TestMain(m *testing.M) {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		service.Startup,
		broker.Startup,
	).Worker(worker.Get())

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.WithoutRedirectStderr().Launch()
	}()

	time.Sleep(time.Second)

	// app.WaitReady(context.Background())

	fmt.Println("TestMain start")

	c := m.Run()

	app.Stop()
	wg.Wait()

	fmt.Println("TestMain done")
	os.Exit(c)
}
