package integration

import (
	"context"
	"fmt"
	"testing"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	pb "google.golang.org/protobuf/proto"
)

func TestScore(t *testing.T) {
	Convey("Score", t, func() {
		config := xkafka.StdConfig()
		writer, err := config.BuildWriter()
		So(err, ShouldBeNil)
		ctx := context.Background()
		clientId := 2008
		requestId := "1234567890"
		b, err := pb.Marshal(&proto.Record{
			ClientId:  2008,
			ModelCode: "active_model",
			Nid:       "2317928",
			Doid:      "ciTdNfuHQLtueBO3cqKLMMXAX4TGY9FsjU4v",
			Ip:        "************",
			Action:    "login",
			AppId:     "5510044",
			Scene:     "active",
			RoleId:    "2304050002",
		})
		So(err, ShouldBeNil)

		err = writer.WriteMessages(ctx, xkafka.Message{
			Key:   []byte(fmt.Sprintf("%d", clientId)),
			Value: b,
			Headers: []xkafka.Header{
				{
					Key:   xnet.RequestId,
					Value: []byte(requestId),
				},
			},
		})
		So(err, ShouldBeNil)
	})
}
