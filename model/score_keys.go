package model

import (
	"context"
	"fmt"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
)

const (
	Dimensions = "%d#%s#%d"
	// BI 标签 与BI标签开发的约定
	//risk:bi:${client_id+#+model_code+#+risk_tag_id}:${nid}
	BIKEY = "risk:bi:%d#%s#%d:%s"

	// 聚集标签
	//risk:bi:${client_id+#+model_code+#+risk_tag_id}:${id_kind}:${nid}
	LCKey = "risk:cl:%s:%s:%s"

	//常用设备
	//risk:ark:${client_id+#+model_code+#+risk_tag_id}:${id_kind}:${id}
	UDKey = "risk:adr:%s:%s:%s"

	//设备风险标签
	//risk:dv:${client_id+#+model_code+#+risk_tag_id}:${id_kind}:${id}
	DVKey = "risk:dv:%s:%s:%s"

	//频率
	//risk:fqs:${client_id+#+model_code+#+risk_tag_id}:${id_kind}:${id}
	LFScoreKey = "risk:fqs:%s:%s:%s"

	// 间隔
	// risk:sintval:${client_id+#+modelId+#+risk_tag_id}:${id_kind}:${id}
	LISKey = "risk:sintval:%s:%s:%s"

	// 常用区域
	//risk:ark:${client_id+#+model_code+#+risk_tag_id}:${id_kind}:${id}
	URKey = "risk:ark:%s:%s:%s"
)

func GetCollectScoreKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(LCKey, dimensions, idKind.String(), id)
	return key
}

func GetDeviceRegularScoreKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(UDKey, dimensions, idKind.String(), id)
	return key
}

func GetDeviceTagScoreKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(DVKey, dimensions, idKind.String(), id)
	return key
}

func GetFrequencyScoreKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(LFScoreKey, dimensions, idKind.String(), id)
	return key
}

func GetIntervalScoreKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(LISKey, dimensions, idKind.String(), id)
	return key
}

func GetIpRegularScoreKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(URKey, dimensions, idKind.String(), id)
	return key
}
