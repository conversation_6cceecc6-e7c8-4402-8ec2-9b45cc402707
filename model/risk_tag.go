package model

import (
	"context"
	"encoding/json"
	"fmt"
	"score/database"
	"score/shared"
	"time"
)

type Param struct {
	IntervalPeriod int64 `json:"interval_period"`
	// IntervalPeriodUnit int64 `json:"interval_period_unit"`
}

type Threshold struct {
	LoginTimes               int64    `json:"login_times,omitempty"`                //登陆次数
	RequestIntervalPeriod    int64    `json:"request_interval_period,omitempty"`    //请求间隔 单位毫秒
	DeviceLoginOpenIDNumbers int64    `json:"device_login_openIDNumbers,omitempty"` //设备聚集登陆数量
	DeviceRiskLabels         []string `json:"device_risk_labels,omitempty"`         //风险设备标签
	DeviceLoginPercentage    float64  `json:"device_login_percentage,omitempty"`    //每次登陆使用的设备占比> %
	DeviceSuccessiveLogin    int64    `json:"device_successive_login,omitempty"`    //最近连续登陆的设备>= 次
	LoginProvincePercentage  float64  `json:"login_province_percentage,omitempty"`  //每次登陆使用的IP所在地区（省）占比> %
	ProvinceSuccessiveLogin  int64    `json:"province_successive_login,omitempty"`  //最近连续登陆的地区（省）>= 次
}

type TagCaliber struct {
	Param     Param     `json:"param,omitempty"`
	Threshold Threshold `json:"threshold,omitempty"`
}

type ScoreRange struct {
	StartScore  int64 `json:"start_score,omitempty"`
	EndScore    int64 `json:"end_score,omitempty"`
	ResultScore int64 `json:"result_score,omitempty"`
	Period      int64 `json:"period,omitempty"`
	Uint        int32 `json:"unit,omitempty"`
}

type RiskTag struct {
	Id         uint64       `json:"id" gorm:"primaryKey"`
	ClientId   int64        `json:"client_id"`
	AppId      string       `json:"app_id" gorm:"column:app_id"`
	ModelId    string       `json:"model_id" gorm:"column:model_id"`
	Name       string       `json:"name"`
	Code       string       `json:"code"`
	Source     int32        `json:"source"`
	Status     int          `json:"status"`
	TagUnit    int32        `json:"tag_unit"`
	RiskType   int          `json:"risk_type"`
	ScoreType  int          `json:"score_type"`
	TagCaliber *TagCaliber  `json:"tag_caliber" gorm:"TYPE:json;serializer:json"`
	ScoreRange []ScoreRange `json:"score_range" gorm:"TYPE:json;serializer:json"`
	IsDel      int          `json:"is_del"`
}

func (r *RiskTag) GetWindow() (time.Duration, error) {
	if r.TagCaliber == nil || r.TagCaliber.Param.IntervalPeriod == 0 {
		return 0, fmt.Errorf("invalid tag caliber risktag id:%d caliber:%v", r.Id, r.TagCaliber)
	}
	return time.Duration(r.TagCaliber.Param.IntervalPeriod) * time.Second, nil
}

func (r *RiskTag) GetThresholdScore() (*Threshold, error) {
	if r.TagCaliber == nil {
		return nil, fmt.Errorf("invalid tag caliber risktag id:%d caliber is nil", r.Id)
	}
	return &r.TagCaliber.Threshold, nil
}

func (r *RiskTag) GetParam() (*Param, error) {
	if r.TagCaliber == nil {
		return nil, shared.ErrRiskTagInvalid
	}
	return &r.TagCaliber.Param, nil
}

func (r *RiskTag) GetNumberScoreAndTLL(n int64) (int64, time.Duration, error) {
	var score int64
	var tll time.Duration
	// 动态分值
	if r.ScoreType == 2 {
		return 0, 0, fmt.Errorf("invalid tag caliber risktag id:%d  score_type equal 2", r.Id)
	}
	if len(r.ScoreRange) < 1 || r.ScoreRange[0].Period == 0 {
		return 0, 0, fmt.Errorf("invalid tag caliber risktag id:%d  ScoreRange:%v", r.Id, r.ScoreRange)
	}
	for _, s := range r.ScoreRange {
		if n >= s.StartScore && n < s.EndScore {
			score = s.ResultScore
			tll = time.Duration(s.Period) * time.Second
			break
		}
	}
	//未命中
	if score == 0 || tll == 0 {
		return 0, 0, nil
	}
	//增减分
	if r.RiskType == 2 {
		score = -score
	}
	return score, tll, nil
}

func (r *RiskTag) GetBoolScoreAndTLL() (int64, time.Duration, error) {
	var score int64
	var tll time.Duration
	// 动态分值
	if r.ScoreType == 2 {
		return 0, 0, fmt.Errorf("invalid tag caliber risktag id:%d  score_type equal 2", r.Id)
	}
	if len(r.ScoreRange) < 1 || r.ScoreRange[0].Period == 0 {
		return 0, 0, fmt.Errorf("invalid tag caliber risktag id:%d  ScoreRange:%v", r.Id, r.ScoreRange)
	}
	score = r.ScoreRange[0].ResultScore
	tll = time.Duration(r.ScoreRange[0].Period) * time.Second
	//未命中
	if score == 0 || tll == 0 {
		return 0, 0, nil
	}
	//增减分
	if r.RiskType == 2 {
		score = -score
	}
	return score, tll, nil
}

// GetRiskTags get all risk tags
func GetRiskTags(ctx context.Context) ([]*RiskTag, error) {
	db := database.Get()
	var riskTags []*RiskTag
	err := db.Table(TableRiskTag).
		Where("status = ?", 1).
		Where("is_del = ?", 0).
		Where("source = ?", 1). //只拿实时标签
		Find(&riskTags).Error
	if err != nil {
		return nil, err
	}
	return riskTags, nil
}

func (r *RiskTag) ToJson() []byte {
	b, _ := json.Marshal(r)
	return b
}
