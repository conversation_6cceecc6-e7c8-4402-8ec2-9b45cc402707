package model

const (
	TableOds       = "score_log_ods"
	TableIp        = "score_log_ip"
	TableDevice    = "score_log_device"
	TableNid       = "score_log_nid"
	TableNidDevice = "score_log_nid_device"
	TableNidRegion = "score_log_nid_region"
	TableDeviceNid = "score_log_device_nid"
	TableIpNid     = "score_log_ip_nid"

	MinuteSubfix = "_minute"
	HourSubfix   = "_hour"
)

// func QueryNidFq(ctx context.Context, clientId uint32, modelCode string, nid string,
// 	startTime time.Time, endTime time.Time) (int64, error) {
// 	tableName, sTime := TableSelect(TableNid, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT sum(login_count) as login_total
// 		FROM %s
// 		WHERE client_id = ? AND model_code = ? AND nid = ?
// 		AND start_interval >= ? AND start_interval <= ?
// 	`, tableName)
// 	var total int64
// 	if err := db.QueryRow(ctx, query, clientId, modelCode,
// 		nid, sTime, endTime).Scan(&total); err != nil {
// 		return 0, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	return total, nil
// }

// func QueryDiodFq(ctx context.Context, clientId uint32, modelCode string, doid string,
// 	startTime time.Time, endTime time.Time) (int64, error) {
// 	tableName, sTime := TableSelect(TableDevice, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT sum(device_count) as device_total
// 		FROM %s
// 		WHERE client_id = ? AND model_code = ? AND doid = ?
// 		AND start_interval >= ? AND start_interval <= ?
// 	`, tableName)
// 	var total int64
// 	if err := db.QueryRow(ctx, query, clientId, modelCode,
// 		doid, sTime, endTime).Scan(&total); err != nil {
// 		return 0, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	return total, nil
// }

// func QueryIpFq(ctx context.Context, clientId uint32, modelCode string, ip string,
// 	startTime time.Time, endTime time.Time) (int64, error) {
// 	tableName, sTime := TableSelect(TableIp, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT sum(ip_count) as ip_total
// 		FROM %s
// 		WHERE client_id = ? AND model_code = ? AND ip = ?
// 		AND start_interval >= ? AND start_interval <= ?
// 	`, tableName)
// 	var total int64
// 	if err := db.QueryRow(ctx, query, clientId, modelCode, ip,
// 		sTime, endTime).Scan(&total); err != nil {
// 		return 0, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	return total, nil
// }

// func QueryDoidAccounts(ctx context.Context, clientId uint32, modelCode string, doid string,
// 	startTime time.Time, endTime time.Time) (map[string]struct{}, error) {
// 	tableName, sTime := TableSelect(TableDeviceNid, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT groupUniqArray(nid) as nid_group
// 		FROM %s
// 		WHERE client_id = ? AND model_code = ? AND doid = ?
// 		AND start_interval >= ? AND start_interval <= ?`,
// 		tableName)
// 	var nids []string
// 	if err := db.QueryRow(ctx, query, clientId, modelCode, doid, sTime, endTime).
// 		Scan(&nids); err != nil {
// 		return nil, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	nidMap := make(map[string]struct{})
// 	for _, nid := range nids {
// 		nidMap[nid] = struct{}{}
// 	}
// 	return nidMap, nil
// }

// func QueryIpAccounts(ctx context.Context, clientId uint32, modelCode string, ip string,
// 	startTime time.Time, endTime time.Time) (map[string]struct{}, error) {
// 	tableName, sTime := TableSelect(TableIpNid, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT groupUniqArray(nid) as nid_list
// 		FROM %s
// 		WHERE client_id = ? AND model_code = ? AND ip = ?
// 		AND start_interval >= ? AND start_interval <= ?`,
// 		tableName)
// 	var nids []string
// 	if err := db.QueryRow(ctx, query, clientId, modelCode, ip,
// 		sTime, endTime).Scan(&nids); err != nil {
// 		return nil, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	nidMap := make(map[string]struct{})
// 	for _, nid := range nids {
// 		nidMap[nid] = struct{}{}
// 	}
// 	return nidMap, nil
// }

// func QueryAccountRegion(ctx context.Context, clientId uint32, modelCode string, nid string, region string,
// 	startTime time.Time, endTime time.Time) (int64, int64, error) {
// 	tableName, sTime := TableSelect(TableNidRegion, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT sum(region_count)                as total_count,
//        		sumIf(region_count, region = ?) as regioncount
// 		from %s
// 		where client_id = ?
// 		  and model_code = ?
// 		  and start_interval >= ?
// 		  and start_interval <= ?
// 		  and nid = ?
// 	`, tableName)
// 	var n1, n2 int64
// 	err := db.QueryRow(ctx, query, region, clientId, modelCode, sTime, endTime, nid).
// 		Scan(&n1, &n2)
// 	if err != nil {
// 		return 0, 0, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	return n1, n2, nil
// }

// func QueryAccountDevice(ctx context.Context, clientId uint32, modelCode string, nid string, doid string,
// 	startTime time.Time, endTime time.Time) (int64, int64, error) {
// 	tableName, sTime := TableSelect(TableNidDevice, startTime, endTime)
// 	db := database.GetClickhouse()
// 	query := fmt.Sprintf(`
// 		SELECT sum(device_count)                as total_count,
//        		sumIf(device_count, doid = ?) as devicecount
// 		from %s
// 		where client_id = ?
// 		  and model_code = ?
// 		  and start_interval >= ?
// 		  and start_interval <= ?
// 		  and nid = ?
// 	`, tableName)
// 	var n1, n2 int64
// 	err := db.QueryRow(ctx, query, doid, clientId, modelCode, sTime, endTime, nid).
// 		Scan(&n1, &n2)
// 	if err != nil {
// 		return 0, 0, fmt.Errorf("failed to execute query: %v", err)
// 	}
// 	return n1, n2, nil
// }

// func BatchInsert(ctx context.Context, data []*pb.Record) error {
// 	batch, err := database.GetClickhouse().PrepareBatch(ctx, "INSERT INTO "+TableOds)
// 	if err != nil {
// 		return err
// 	}
// 	for _, d := range data {
// 		err := batch.Append(
// 			d.ClientId,
// 			d.ModelCode,
// 			d.AppId,
// 			d.Action,
// 			d.Nid,
// 			d.Doid,
// 			d.Ip,
// 			d.Province,
// 			time.Now().Format("2006-01-02 15:04:05"),
// 		)
// 		if err != nil {
// 			return err
// 		}
// 	}
// 	if err := batch.Send(); err != nil {
// 		return err
// 	}
// 	return nil
// }

// // > 2 days 以上使用小时表
// // 否则使用分钟表
// func TableSelect(tableName string, startTime time.Time, endTime time.Time) (string, time.Time) {
// 	if endTime.Sub(startTime) > 24*2*time.Hour {
// 		return tableName + HourSubfix, startTime.Truncate(time.Hour) // 使用小时表
// 	} else {
// 		return tableName + MinuteSubfix, startTime.Truncate(time.Minute) // 默认使用分钟表
// 	}
// }
