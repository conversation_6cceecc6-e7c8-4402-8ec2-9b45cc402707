package model

// import (
// 	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
// )

// const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

// func TestQueryNidFq(t *testing.T) {
// 	Convey("QueryNidFq", t, func() {
// 		n, err := QueryNidFq(context.TODO(), 1033, "risk_model_all",
// 			"**********", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		So(n, ShouldEqual, 3)
// 	})
// }

// func TestQueryDiodFq(t *testing.T) {
// 	Convey("QueryDiodFq", t, func() {
// 		n, err := QueryDiodFq(context.TODO(), 1033, "risk_model_all",
// 			"********************************", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		So(n, ShouldEqual, 3)
// 	})
// }

// func TestQueryIpFq(t *testing.T) {
// 	Convey("QueryIpFq", t, func() {
// 		n, err := QueryIpFq(context.TODO(), 1033, "risk_model_all",
// 			"************", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		So(n, ShouldEqual, 3)
// 	})
// }

// func TestQueryDoidAccounts(t *testing.T) {
// 	Convey("QueryDiodAccounts", t, func() {
// 		retMap, err := QueryDoidAccounts(context.TODO(), 1033, "risk_model_all",
// 			"********************************", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		t.Log(retMap)
// 		So(len(retMap), ShouldEqual, 3)
// 	})
// }

// func TestQueryIpAccounts(t *testing.T) {
// 	Convey("QueryIpAccounts", t, func() {
// 		retMap, err := QueryIpAccounts(context.TODO(), 1033, "risk_model_all",
// 			"************", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		t.Log(retMap)
// 		So(len(retMap), ShouldEqual, 3)
// 	})
// }

// func TestQueryAccountRegion(t *testing.T) {
// 	Convey("QueryAccountRegion", t, func() {
// 		n1, n2, err := QueryAccountRegion(context.TODO(), 1033, "risk_model_all",
// 			"**********", "美国", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		So(n1, ShouldEqual, 3)
// 		So(n2, ShouldEqual, 3)
// 	})
// }

// func TestQueryAccountDivece(t *testing.T) {
// 	Convey("QueryAccountDivece", t, func() {
// 		n1, n2, err := QueryAccountDevice(context.TODO(), 1033, "risk_model_all",
// 			"**********", "********************************", time.Now().Add(-24*time.Hour), time.Now())
// 		So(err, ShouldEqual, nil)
// 		So(n1, ShouldEqual, 3)
// 		So(n2, ShouldEqual, 3)
// 	})
// }

// func BenchmarkClickhouseQueryNidFq(b *testing.B) {
// 	rand.New(rand.NewSource(time.Now().UnixNano()))
// 	for i := 0; i < b.N; i++ {
// 		QueryNidFq(context.TODO(), 1008, "model_web",
// 			randomNid(), time.Now().Add(-24*time.Hour), time.Now())
// 	}
// }

// func BenchmarkClickhouseQueryDiodFq(b *testing.B) {
// 	rand.New(rand.NewSource(time.Now().UnixNano()))
// 	for i := 0; i < b.N; i++ {
// 		QueryDiodFq(context.TODO(), 1008, "model_web",
// 			generateRandomString(32), time.Now().Add(-24*time.Hour), time.Now())
// 	}
// }

// func BenchmarkClickhouseQueryIpFq(b *testing.B) {
// 	rand.New(rand.NewSource(time.Now().UnixNano()))
// 	b.ResetTimer()
// 	for i := 0; i < b.N; i++ {
// 		QueryIpFq(context.TODO(), 1008, "model_web",
// 			randomIPv4(), time.Now().Add(-24*time.Hour), time.Now())
// 	}
// }

// func BenchmarkClickhouseQueryDoidAccounts(b *testing.B) {
// 	rand.New(rand.NewSource(time.Now().UnixNano()))
// 	for i := 0; i < b.N; i++ {
// 		QueryDoidAccounts(context.TODO(), 1033, "risk_model_all",
// 			generateRandomString(32), time.Now().Add(-24*time.Hour), time.Now())
// 	}
// }

// func randomIPv4() string {
// 	return net.IPv4(
// 		byte(rand.Intn(256)),
// 		byte(rand.Intn(256)),
// 		byte(rand.Intn(256)),
// 		byte(rand.Intn(256))).String()
// }

// func randomNid() string {
// 	return strconv.FormatInt(rand.Int63n(*********), 10)
// }

// func generateRandomString(length int) string {
// 	b := make([]byte, length)
// 	for i := range b {
// 		b[i] = charset[rand.Intn(len(charset))]
// 	}
// 	return string(b)
// }

// func TestBatchInsert(t *testing.T) {
// 	Convey("BatchInsert", t, func() {
// 		err := BatchInsert(
// 			context.TODO(),
// 			[]*pb.Record{
// 				{
// 					Nid:       "**********",
// 					Doid:      "********************************",
// 					Ip:        "************",
// 					ClientId:  1033,
// 					ModelCode: "risk_model_all",
// 					Province:  "北京市",
// 					AppId:     "aaaa",
// 					Action:    "login",
// 				},
// 			},
// 		)
// 		So(err, ShouldEqual, nil)
// 	})
// }

// func TestDeviceNids(t *testing.T) {
// 	s := time.Now()
// 	wg := &sync.WaitGroup{}

// 	for i := 0; i < 100; i++ {
// 		wg.Add(1)
// 		go func(i int) {
// 			defer wg.Done()
// 			for i := 0; i < 1000; i++ {
// 				// st := time.Now()
// 				QueryDoidAccounts(context.TODO(), 1033, "risk_model_all",
// 					generateRandomString(32), time.Now().Add(-24*time.Hour), time.Now())
// 				// end := time.Now()
// 				// t.Log(end.Sub(st))
// 			}
// 		}(i)
// 	}
// 	wg.Wait()
// 	t.Log(time.Now().Sub(s))
// }
