package model

import (
	"context"
	"fmt"
	"score/database"
	"time"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
)

var (
	//risk:cld::${client_id+#+model_code+#+risk_tag_id}:${id_kind}:${id}
	CLDKey = "risk:cld:%s:%s:%s"
)

func GetCollectKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(CLDKey, dimensions, idKind.String(), id)
	return key
}

func CollectLimiter(ctx context.Context, key string, member string, windowSize time.Duration, maxRequests uint64) (bool, error) {
	now := time.Now().UnixNano()
	windowStart := now - int64(windowSize)
	rdb := database.GetRdb()
	var count *xredis.IntCmd
	_, err := rdb.Pipelined(ctx, func(pipe xredis.Pipeliner) error {
		pipe.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", windowStart))
		pipe.ZAdd(ctx, key, xredis.Z{
			Score:  float64(now),
			Member: member,
		})
		count = pipe.ZCard(ctx, key)
		pipe.Expire(ctx, key, windowSize*2)
		return nil
	})
	if err != nil {
		return false, err
	}
	n, err := count.Uint64()
	if err != nil {
		return false, err
	}
	//删除多余的key
	if n > maxRequests {
		err := rdb.ZRemRangeByRank(ctx, key, 0, int64(n)-int64(maxRequests)-1).Err()
		if err != nil {
			return false, err
		}
		return true, nil
	}
	return int64(n) >= int64(maxRequests), nil
}
