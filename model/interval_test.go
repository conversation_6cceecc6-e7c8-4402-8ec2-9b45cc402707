package model

import (
	"context"
	"score/config"
	"score/database"
	"testing"
	"time"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func TestMain(m *testing.M) {
	xconf.ReadInConfig()
	err := config.Startup()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}
	err = xlog.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	err = xdebug.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	m.Run()
}

func TestSetLastLoginTime(t *testing.T) {
	data := []struct {
		clientId  uint32
		modelCode string
		riskId    uint64
		idKind    proto.IdKind
		id        string
		t         time.Time
		ttl       time.Duration
	}{
		{
			clientId:  1008,
			modelCode: "ios",
			riskId:    1,
			idKind:    proto.IdKind_NId,
			id:        "1",
			t:         time.Now(),
			ttl:       time.Hour * 2,
		},
		{

			clientId:  1008,
			modelCode: "ios",
			riskId:    2,
			idKind:    proto.IdKind_DoId,
			id:        "1874444444",
			t:         time.Now(),
			ttl:       time.Hour * 2,
		},
		{
			clientId:  1008,
			modelCode: "ios",
			riskId:    3,
			idKind:    proto.IdKind_Ip,
			id:        "************",
			t:         time.Now(),
			ttl:       time.Hour * 2,
		},
	}

	Convey("test SetLastLoginTime", t, func(c C) {
		for _, d := range data {
			err := SetLastTime(context.Background(), d.clientId, d.modelCode, d.riskId, d.idKind, d.id, d.t, d.ttl)
			So(err, ShouldBeNil)
			n, err := GetLastTime(context.Background(), d.clientId, d.modelCode, d.riskId, d.idKind, d.id)
			So(err, ShouldBeNil)
			So(d.t.UnixMilli(), ShouldEqual, n)
			e, err := GetExpire(context.Background(), d.clientId, d.modelCode, d.riskId, d.idKind, d.id)
			So(err, ShouldBeNil)
			So(e, ShouldBeGreaterThan, time.Second*1)
		}
		//wait for log flush
		time.Sleep(1 * time.Second)
	})
}
