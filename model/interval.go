package model

import (
	"context"
	"errors"
	"fmt"
	"score/database"
	"time"

	proto "gitlab.papegames.com/biz/protobuf/risk/livetags"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
)

var (
	// risk:interval:${client_id+#+modelId+#+risk_tag_id}:${id_kind}:${id}
	LIKey       = "risk:intval:%s:%s:%s"
	IntervalTTL = 1 * time.Hour
)

func GetLastTime(ctx context.Context, key string) (int64, error) {
	rdb := database.GetRdb()
	n, err := rdb.Get(ctx, key).Int64()
	if err != nil {
		if errors.Is(xredis.Nil, err) {
			return 0, nil
		} else {
			return 0, err
		}
	}
	return n, nil
}

func SetLastTime(ctx context.Context, key string, t time.Time, ttl time.Duration) error {
	rdb := database.GetRdb()
	return rdb.Set(ctx, key, t.Unix<PERSON>illi(), ttl).Err()
}

// GetExpire get expire time for test
func GetExpire(ctx context.Context, key string) (time.Duration, error) {
	rdb := database.GetRdb()
	return rdb.TTL(ctx, key).Result()
}

func GetIntervalKey(ctx context.Context, clientId uint32, modelCode string, riskId uint64,
	idKind proto.IdKind, id string) string {
	dimensions := fmt.Sprintf(Dimensions, clientId, modelCode, riskId)
	key := fmt.Sprintf(LIKey, dimensions, idKind.String(), id)
	return key
}
