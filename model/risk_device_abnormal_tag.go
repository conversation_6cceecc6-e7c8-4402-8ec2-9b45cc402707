package model

import (
	"score/database"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
)

type RiskDeviceAbnormalTag struct {
	Id      int      `json:"id"`
	DOID    string   `gorm:"column:DOID" json:"DOID"`
	RiskTag []string `gorm:"TYPE:json;serializer:json" json:"risk_tag"`
}

func GetRiskDeviceAbnormalTagByDoid(doid string) ([]string, error) {
	db := database.Get()
	var tag RiskDeviceAbnormalTag
	err := db.Table(TableRiskDeviceAbnormalTag).
		Where("DOID = ?", doid).
		Where("is_deleted = ?", 0).
		First(&tag).Error
	if err != nil {
		return nil, xgorm.SkipRecordNotFound(err)
	}
	return tag.RiskTag, nil
}
